#!/bin/bash

# 测试流程定义列表接口
# 使用方法: ./test_process_definition_list.sh

# 配置
BASE_URL="http://localhost:48080"
API_PATH="/admin-api/bpm/process-definition/list"
CONTENT_TYPE="application/json"

# 获取访问令牌（需要根据实际情况修改）
# TOKEN="your_access_token_here"

echo "=== 测试流程定义列表接口（分页版本） ==="
echo "URL: ${BASE_URL}${API_PATH}"
echo ""

# 测试1：基本分页查询
echo "📋 测试1：基本分页查询"
TEST_DATA_1='{
  "pageNo": 1,
  "pageSize": 5,
  "suspensionState": 1
}'

echo "请求数据: $TEST_DATA_1"
if [ -n "$TOKEN" ]; then
    RESPONSE_1=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -H "Authorization: Bearer $TOKEN" \
        -G \
        --data-urlencode "pageNo=1" \
        --data-urlencode "pageSize=5" \
        --data-urlencode "suspensionState=1" \
        "${BASE_URL}${API_PATH}")
else
    RESPONSE_1=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -G \
        --data-urlencode "pageNo=1" \
        --data-urlencode "pageSize=5" \
        --data-urlencode "suspensionState=1" \
        "${BASE_URL}${API_PATH}")
fi

HTTP_CODE_1=$(echo "$RESPONSE_1" | grep "HTTP_CODE:" | cut -d: -f2)
BODY_1=$(echo "$RESPONSE_1" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE_1"
echo "响应内容:"
echo "$BODY_1" | jq '.' 2>/dev/null || echo "$BODY_1"
echo ""

# 测试2：按分类查询
echo "📋 测试2：按分类查询"
if [ -n "$TOKEN" ]; then
    RESPONSE_2=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -H "Authorization: Bearer $TOKEN" \
        -G \
        --data-urlencode "pageNo=1" \
        --data-urlencode "pageSize=10" \
        --data-urlencode "suspensionState=1" \
        --data-urlencode "category=OA" \
        "${BASE_URL}${API_PATH}")
else
    RESPONSE_2=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -G \
        --data-urlencode "pageNo=1" \
        --data-urlencode "pageSize=10" \
        --data-urlencode "suspensionState=1" \
        --data-urlencode "category=OA" \
        "${BASE_URL}${API_PATH}")
fi

HTTP_CODE_2=$(echo "$RESPONSE_2" | grep "HTTP_CODE:" | cut -d: -f2)
BODY_2=$(echo "$RESPONSE_2" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE_2"
echo "响应内容:"
echo "$BODY_2" | jq '.' 2>/dev/null || echo "$BODY_2"
echo ""

# 测试3：按名称搜索
echo "📋 测试3：按名称搜索"
if [ -n "$TOKEN" ]; then
    RESPONSE_3=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -H "Authorization: Bearer $TOKEN" \
        -G \
        --data-urlencode "pageNo=1" \
        --data-urlencode "pageSize=10" \
        --data-urlencode "suspensionState=1" \
        --data-urlencode "name=测试" \
        "${BASE_URL}${API_PATH}")
else
    RESPONSE_3=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -G \
        --data-urlencode "pageNo=1" \
        --data-urlencode "pageSize=10" \
        --data-urlencode "suspensionState=1" \
        --data-urlencode "name=测试" \
        "${BASE_URL}${API_PATH}")
fi

HTTP_CODE_3=$(echo "$RESPONSE_3" | grep "HTTP_CODE:" | cut -d: -f2)
BODY_3=$(echo "$RESPONSE_3" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE_3"
echo "响应内容:"
echo "$BODY_3" | jq '.' 2>/dev/null || echo "$BODY_3"
echo ""

# 测试4：测试精简列表接口
echo "📋 测试4：精简列表接口"
SIMPLE_API_PATH="/admin-api/bpm/process-definition/simple-list"
if [ -n "$TOKEN" ]; then
    RESPONSE_4=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        -H "Authorization: Bearer $TOKEN" \
        "${BASE_URL}${SIMPLE_API_PATH}")
else
    RESPONSE_4=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X GET \
        -H "Content-Type: $CONTENT_TYPE" \
        "${BASE_URL}${SIMPLE_API_PATH}")
fi

HTTP_CODE_4=$(echo "$RESPONSE_4" | grep "HTTP_CODE:" | cut -d: -f2)
BODY_4=$(echo "$RESPONSE_4" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE_4"
echo "响应内容:"
echo "$BODY_4" | jq '.' 2>/dev/null || echo "$BODY_4"
echo ""

# 总结
echo "=== 测试总结 ==="
if [ "$HTTP_CODE_1" = "200" ]; then
    echo "✅ 基本分页查询: 成功"
else
    echo "❌ 基本分页查询: 失败 (HTTP $HTTP_CODE_1)"
fi

if [ "$HTTP_CODE_2" = "200" ]; then
    echo "✅ 分类查询: 成功"
else
    echo "❌ 分类查询: 失败 (HTTP $HTTP_CODE_2)"
fi

if [ "$HTTP_CODE_3" = "200" ]; then
    echo "✅ 名称搜索: 成功"
else
    echo "❌ 名称搜索: 失败 (HTTP $HTTP_CODE_3)"
fi

if [ "$HTTP_CODE_4" = "200" ]; then
    echo "✅ 精简列表: 成功"
else
    echo "❌ 精简列表: 失败 (HTTP $HTTP_CODE_4)"
fi

echo ""
echo "💡 使用提示:"
echo "1. 请确保服务已启动在 $BASE_URL"
echo "2. 如需认证，请设置 TOKEN 变量"
echo "3. 可以修改测试参数来验证不同场景"
echo "4. 查看响应数据确认分页和过滤功能"
