# 流程定义列表接口优化说明

## 优化内容

原有的 `/admin-api/bpm/process-definition/list` 接口一次性查询所有流程定义，数据量大时性能较差。现已优化为支持分类查询和分页查询。

## 新接口设计

### 1. 主要列表接口（分页）
**接口路径**: `GET /admin-api/bpm/process-definition/list`

**请求参数**:
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "suspensionState": 1,
  "category": "OA",
  "name": "请假",
  "key": "leave",
  "visibleOnly": true,
  "canStartOnly": true
}
```

**参数说明**:
- `pageNo`: 页码，从1开始
- `pageSize`: 每页大小
- `suspensionState`: 挂起状态（1=激活，2=挂起）
- `category`: 流程分类编码（可选）
- `name`: 流程名称模糊查询（可选）
- `key`: 流程标识模糊查询（可选）
- `visibleOnly`: 是否只显示可见流程（默认true）
- `canStartOnly`: 是否只显示当前用户可发起的流程（默认true）

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": "process_def_id",
        "name": "请假流程",
        "key": "leave_process",
        "category": "OA",
        "categoryName": "办公自动化",
        "version": 1,
        "suspensionState": 1,
        "description": "员工请假申请流程"
      }
    ],
    "total": 50
  },
  "msg": "操作成功"
}
```

### 2. 完整列表接口（不分页）
**接口路径**: `GET /admin-api/bpm/process-definition/all-list`

用于管理界面需要获取所有流程定义的场景。

**请求参数**:
- `suspensionState`: 挂起状态（必填）

### 3. 精简列表接口（保持不变）
**接口路径**: `GET /admin-api/bpm/process-definition/simple-list`

用于下拉选择等场景，只返回基本信息。

## 使用场景

### 场景1：流程管理页面
```javascript
// 分页查询所有流程
const params = {
  pageNo: 1,
  pageSize: 20,
  suspensionState: 1, // 只查询激活的流程
  visibleOnly: true,
  canStartOnly: false // 管理员可以看到所有流程
}
```

### 场景2：按分类查询
```javascript
// 查询OA分类下的流程
const params = {
  pageNo: 1,
  pageSize: 10,
  suspensionState: 1,
  category: "OA"
}
```

### 场景3：用户发起流程页面
```javascript
// 查询用户可发起的流程
const params = {
  pageNo: 1,
  pageSize: 10,
  suspensionState: 1,
  visibleOnly: true,
  canStartOnly: true // 只显示当前用户可发起的流程
}
```

### 场景4：搜索流程
```javascript
// 按名称搜索
const params = {
  pageNo: 1,
  pageSize: 10,
  suspensionState: 1,
  name: "请假" // 模糊搜索包含"请假"的流程
}
```

## 性能优化

1. **分页查询**: 避免一次性加载大量数据
2. **条件过滤**: 在数据库层面进行初步过滤
3. **权限过滤**: 在应用层进行精确的权限控制
4. **缓存优化**: 流程定义信息和分类信息支持缓存

## 向后兼容

- 原有的 `/list` 接口保持可用，但返回格式从 `List` 改为 `PageResult`
- 如果前端没有传分页参数，会使用默认值
- 所有原有参数保持兼容

## 前端适配建议

1. **更新接口调用**: 适配新的分页响应格式
2. **添加分页组件**: 支持分页展示
3. **添加搜索功能**: 支持按分类、名称等条件搜索
4. **优化用户体验**: 添加加载状态、空数据提示等

## 测试建议

1. 测试分页功能是否正常
2. 测试各种过滤条件的组合
3. 测试权限控制是否生效
4. 测试性能是否有改善
