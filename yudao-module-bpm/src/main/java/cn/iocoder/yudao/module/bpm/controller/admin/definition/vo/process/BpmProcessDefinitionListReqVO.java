package cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.process;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 流程定义列表查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmProcessDefinitionListReqVO extends PageParam {

    @Schema(description = "挂起状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer suspensionState; // 参见 Flowable SuspensionState 枚举

    @Schema(description = "流程分类编码", example = "OA")
    private String category;

    @Schema(description = "流程名称", example = "请假流程")
    private String name;

    @Schema(description = "流程标识", example = "leave_process")
    private String key;

    @Schema(description = "是否只显示可见的流程", example = "true")
    private Boolean visibleOnly = true;

    @Schema(description = "是否只显示当前用户可发起的流程", example = "true")
    private Boolean canStartOnly = true;

}
