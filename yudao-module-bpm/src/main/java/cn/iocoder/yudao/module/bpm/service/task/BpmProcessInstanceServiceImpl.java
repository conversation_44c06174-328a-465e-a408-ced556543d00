package cn.iocoder.yudao.module.bpm.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.framework.common.util.object.PageUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.model.BpmModelMetaInfoVO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.model.simple.BpmSimpleModelNodeVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.*;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmApprovalDetailRespVO.ActivityNodeTask;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import cn.iocoder.yudao.module.bpm.convert.task.BpmProcessInstanceConvert;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessDefinitionInfoDO;
import cn.iocoder.yudao.module.bpm.dal.redis.BpmProcessIdRedisDAO;
import cn.iocoder.yudao.module.bpm.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.bpm.enums.definition.BpmModelTypeEnum;
import cn.iocoder.yudao.module.bpm.enums.definition.BpmSimpleModelNodeTypeEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmReasonEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.candidate.BpmTaskCandidateInvoker;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmTaskCandidateStrategyEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmnModelConstants;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmnVariableConstants;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.event.BpmProcessInstanceEventPublisher;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.BpmHttpRequestUtils;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.BpmnModelUtils;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.SimpleModelUtils;
import cn.iocoder.yudao.module.bpm.service.definition.BpmProcessDefinitionService;
import cn.iocoder.yudao.module.bpm.service.message.BpmMessageService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import java.time.LocalDateTime;
import org.flowable.bpmn.model.*;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmApprovalDetailRespVO.ActivityNode;
import static cn.iocoder.yudao.module.bpm.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmnModelConstants.START_USER_NODE_ID;
import static cn.iocoder.yudao.module.bpm.framework.flowable.core.util.BpmnModelUtils.parseNodeType;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.flowable.bpmn.constants.BpmnXMLConstants.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 流程实例 Service 实现类
 * <p>
 * ProcessDefinition & ProcessInstance & Execution & Task 的关系：
 * 1. <a href="https://blog.csdn.net/bobozai86/article/details/105210414" />
 * <p>
 * HistoricProcessInstance & ProcessInstance 的关系：
 * 1. <a href=" https://my.oschina.net/843294669/blog/71902" />
 * <p>
 * 简单来说，前者 = 历史 + 运行中的流程实例，后者仅是运行中的流程实例
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmProcessInstanceServiceImpl implements BpmProcessInstanceService {

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private HistoryService historyService;
    @Resource
    private RepositoryService repositoryService;

    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    @Lazy // 避免循环依赖
    private BpmTaskService taskService;
    @Resource
    private BpmMessageService messageService;

    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private cn.iocoder.yudao.module.system.service.dict.DictDataService dictDataService;

    @Resource
    private BpmProcessInstanceEventPublisher processInstanceEventPublisher;

    @Resource
    private BpmTaskCandidateInvoker taskCandidateInvoker;

    @Resource
    private BpmProcessIdRedisDAO processIdRedisDAO;

    @Resource
    private JdbcTemplate jdbcTemplate;

    // ========== Query 查询相关方法 ==========

    @Override
    public ProcessInstance getProcessInstance(String id) {
        return runtimeService.createProcessInstanceQuery()
                .includeProcessVariables()
                .processInstanceId(id)
                .singleResult();
    }

    @Override
    public List<ProcessInstance> getProcessInstances(Set<String> ids) {
        return runtimeService.createProcessInstanceQuery().processInstanceIds(ids).includeProcessVariables().list();
    }

    @Override
    public HistoricProcessInstance getHistoricProcessInstance(String id) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceId(id).includeProcessVariables()
                .singleResult();
    }

    @Override
    public List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceIds(ids).includeProcessVariables()
                .list();
    }

    private Map<String, String> getFormFieldsPermission(BpmnModel bpmnModel,
                                                        String activityId, String taskId) {
        // 1. 获取流程活动编号。流程活动 Id 为空事，从流程任务中获取流程活动 Id
        if (StrUtil.isEmpty(activityId) && StrUtil.isNotEmpty(taskId)) {
            activityId = Optional.ofNullable(taskService.getHistoricTask(taskId))
                    .map(HistoricTaskInstance::getTaskDefinitionKey).orElse(null);
        }
        if (StrUtil.isEmpty(activityId)) {
            return null;
        }

        // 2. 从 BpmnModel 中解析表单字段权限
        return BpmnModelUtils.parseFormFieldsPermission(bpmnModel, activityId);
    }

    @Override
    public String getProcessInstanceName(String processInstanceId) {
        HistoricProcessInstance processInstance = getHistoricProcessInstance(processInstanceId);
        if (processInstance == null) {
            throw exception(ErrorCodeConstants.PROCESS_INSTANCE_NOT_EXISTS);
        }
        return processInstance.getName();
    }

    @Override
    public BpmApprovalDetailRespVO getApprovalDetail(Long loginUserId, BpmApprovalDetailReqVO reqVO) {
        // 1.1 从 reqVO 中，读取公共变量
        Long startUserId = loginUserId; // 流程发起人
        HistoricProcessInstance historicProcessInstance = null; // 流程实例
        Integer processInstanceStatus = BpmProcessInstanceStatusEnum.NOT_START.getStatus(); // 流程状态
        Map<String, Object> processVariables = new HashMap<>(); // 流程变量
        // 1.2 如果是流程已发起的场景，则使用流程实例的数据
        if (reqVO.getProcessInstanceId() != null) {
            historicProcessInstance = getHistoricProcessInstance(reqVO.getProcessInstanceId());
            if (historicProcessInstance == null) {
                throw exception(ErrorCodeConstants.PROCESS_INSTANCE_NOT_EXISTS);
            }
            startUserId = Long.valueOf(historicProcessInstance.getStartUserId());
            processInstanceStatus = FlowableUtils.getProcessInstanceStatus(historicProcessInstance);
            // 合并 DB 和前端传递的流量变量，以前端的为主
            if (CollUtil.isNotEmpty(historicProcessInstance.getProcessVariables())) {
                processVariables.putAll(historicProcessInstance.getProcessVariables());
            }
        }
        if (CollUtil.isNotEmpty(reqVO.getProcessVariables())) {
            processVariables.putAll(reqVO.getProcessVariables());
        }
        // 1.3 读取其它相关数据
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinition(
                historicProcessInstance != null ? historicProcessInstance.getProcessDefinitionId()
                        : reqVO.getProcessDefinitionId());
        BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService
                .getProcessDefinitionInfo(processDefinition.getId());
        BpmnModel bpmnModel = processDefinitionService.getProcessDefinitionBpmnModel(processDefinition.getId());

        // 2.1 已结束 + 进行中的活动节点
        List<ActivityNode> endActivityNodes = null; // 已结束的审批信息
        List<ActivityNode> runActivityNodes = null; // 进行中的审批信息
        List<HistoricActivityInstance> activities = null; // 流程实例列表
        if (reqVO.getProcessInstanceId() != null) {
            activities = taskService.getActivityListByProcessInstanceId(reqVO.getProcessInstanceId());
            List<HistoricTaskInstance> tasks = taskService.getTaskListByProcessInstanceId(reqVO.getProcessInstanceId(),
                    true);
            endActivityNodes = getEndActivityNodeList(startUserId, bpmnModel, processDefinitionInfo,
                    historicProcessInstance, processInstanceStatus, activities, tasks);
            runActivityNodes = getRunApproveNodeList(startUserId, bpmnModel, processDefinition, processVariables,
                    activities, tasks);
        }

        // 2.2 流程已经结束，直接 return，无需预测
        if (BpmProcessInstanceStatusEnum.isProcessEndStatus(processInstanceStatus)) {
            return buildApprovalDetail(reqVO, bpmnModel, processDefinition, processDefinitionInfo,
                    historicProcessInstance,
                    processInstanceStatus, endActivityNodes, runActivityNodes, null, null);
        }

        // 3.1 计算当前登录用户的待办任务
        BpmTaskRespVO todoTask = taskService.getTodoTask(loginUserId, reqVO.getTaskId(), reqVO.getProcessInstanceId());
        // 3.2 预测未运行节点的审批信息
        List<ActivityNode> simulateActivityNodes = getSimulateApproveNodeList(startUserId, bpmnModel,
                processDefinitionInfo,
                processVariables, activities);
        // 3.3 如果是发起动作，activityId 为开始节点，不校验审批人自选节点
        if (ObjUtil.equals(reqVO.getActivityId(), BpmnModelConstants.START_USER_NODE_ID)) {
            simulateActivityNodes.removeIf(node ->
                    BpmTaskCandidateStrategyEnum.APPROVE_USER_SELECT.getStrategy().equals(node.getCandidateStrategy()));
        }

        // 4. 拼接最终数据
        return buildApprovalDetail(reqVO, bpmnModel, processDefinition, processDefinitionInfo, historicProcessInstance,
                processInstanceStatus, endActivityNodes, runActivityNodes, simulateActivityNodes, todoTask);
    }

    @Override
    public List<ActivityNode> getNextApprovalNodes(Long loginUserId, BpmApprovalDetailReqVO reqVO) {
        // 1.1 校验任务存在，且是当前用户的
        Task task = taskService.validateTask(loginUserId, reqVO.getTaskId());
        // 1.2 校验流程实例存在
        ProcessInstance instance = getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(task.getProcessInstanceId());
        if (historicProcessInstance == null) {
            throw exception(ErrorCodeConstants.PROCESS_INSTANCE_NOT_EXISTS);
        }
        // 1.3 校验BpmnModel
        BpmnModel bpmnModel = processDefinitionService.getProcessDefinitionBpmnModel(task.getProcessDefinitionId());
        if (bpmnModel == null) {
            return null;
        }

        // 2. 设置流程变量
        Map<String, Object> processVariables = new HashMap<>();
        // 2.1 获取历史中流程变量
        if (CollUtil.isNotEmpty(historicProcessInstance.getProcessVariables())) {
            processVariables.putAll(historicProcessInstance.getProcessVariables());
        }
        // 2.2 合并前端传递的流程变量，以前端为准
        if (CollUtil.isNotEmpty(reqVO.getProcessVariables())) {
            processVariables.putAll(reqVO.getProcessVariables());
        }

        // 3. 获取下一个将要执行的节点集合
        FlowElement flowElement = bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        List<FlowNode> nextFlowNodes = BpmnModelUtils.getNextFlowNodes(flowElement, bpmnModel, processVariables);
        
        // 递归查找下一个用户任务节点
        List<FlowNode> userTaskNodes = new ArrayList<>();
        for (FlowNode nextNode : nextFlowNodes) {
            findUserTaskNodes(nextNode, bpmnModel, processVariables, userTaskNodes, new HashSet<>());
        }
        
        // 如果没有找到用户任务节点，返回空列表
        if (userTaskNodes.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<ActivityNode> nextActivityNodes = convertList(userTaskNodes, node -> {
            Integer candidateStrategy = BpmnModelUtils.parseCandidateStrategy(node);
            // 如果没有候选人策略，则跳过该节点
            if (candidateStrategy == null) {
                return null;
            }
            
            return new ActivityNode().setId(node.getId())
                .setName(node.getName()).setNodeType(BpmSimpleModelNodeTypeEnum.APPROVE_NODE.getType())
                .setStatus(BpmTaskStatusEnum.RUNNING.getStatus())
                .setCandidateStrategy(candidateStrategy)
                .setCandidateUserIds(getTaskCandidateUserList(bpmnModel, node.getId(),
                        loginUserId, historicProcessInstance.getProcessDefinitionId(), processVariables));
        });
        
        // 移除空节点
        nextActivityNodes.removeIf(Objects::isNull);
        
        if (CollUtil.isNotEmpty(nextActivityNodes)) {
            // 4. 拼接基础信息
            Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(
                    convertSetByFlatMap(nextActivityNodes, ActivityNode::getCandidateUserIds, Collection::stream));
            Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(convertSet(userMap.values(), AdminUserRespDTO::getDeptId));
            nextActivityNodes.forEach(node -> node.setCandidateUsers(convertList(node.getCandidateUserIds(), userId -> {
                AdminUserRespDTO user = userMap.get(userId);
                if (user != null) {
                    return BpmProcessInstanceConvert.INSTANCE.buildUser(userId, userMap, deptMap);
                }
                return null;
            })));
            return nextActivityNodes;
        }
        
        return Collections.emptyList();
    }
    
    /**
     * 递归查找用户任务节点
     * 
     * @param flowNode 当前流程节点
     * @param bpmnModel BPMN模型
     * @param variables 流程变量
     * @param userTaskNodes 找到的用户任务节点列表
     * @param visitedNodes 已访问的节点ID集合，用于避免循环
     */
    private void findUserTaskNodes(FlowNode flowNode, BpmnModel bpmnModel, Map<String, Object> variables, 
                                  List<FlowNode> userTaskNodes, Set<String> visitedNodes) {
        // 如果已经访问过该节点，则返回，避免循环
        if (visitedNodes.contains(flowNode.getId())) {
            return;
        }
        visitedNodes.add(flowNode.getId());
        
        // 如果是用户任务节点，则添加到结果列表中
        if (flowNode instanceof UserTask) {
            userTaskNodes.add(flowNode);
            return; // 找到用户任务节点后，不再继续向下查找
        }
        
        // 如果是其他类型节点（如网关），则继续查找其后续节点
        List<FlowNode> nextNodes = BpmnModelUtils.getNextFlowNodes(flowNode, bpmnModel, variables);
        for (FlowNode nextNode : nextNodes) {
            findUserTaskNodes(nextNode, bpmnModel, variables, userTaskNodes, visitedNodes);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public PageResult<HistoricProcessInstance> getProcessInstancePage(Long userId,
                                                                      BpmProcessInstancePageReqVO pageReqVO) {
        // 1. 构建查询条件
        HistoricProcessInstanceQuery processInstanceQuery = historyService.createHistoricProcessInstanceQuery()
                .includeProcessVariables()
                .processInstanceTenantId(FlowableUtils.getTenantId())
                .orderByProcessInstanceStartTime().desc();
        if (userId != null) { // 【我的流程】菜单时，需要传递该字段
            processInstanceQuery.startedBy(String.valueOf(userId));
        } else if (pageReqVO.getStartUserId() != null) { // 【管理流程】菜单时，才会传递该字段
            processInstanceQuery.startedBy(String.valueOf(pageReqVO.getStartUserId()));
        }
        if (StrUtil.isNotEmpty(pageReqVO.getName())) {
            processInstanceQuery.processInstanceNameLike("%" + pageReqVO.getName() + "%");
        }
        if (StrUtil.isNotEmpty(pageReqVO.getProcessDefinitionKey())) {
            processInstanceQuery.processDefinitionKey(pageReqVO.getProcessDefinitionKey());
        }
        if (StrUtil.isNotEmpty(pageReqVO.getCategory())) {
            processInstanceQuery.processDefinitionCategory(pageReqVO.getCategory());
        }
        if (pageReqVO.getStatus() != null) {
            processInstanceQuery.variableValueEquals(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS,
                    pageReqVO.getStatus());
        }
        if (ArrayUtil.isNotEmpty(pageReqVO.getCreateTime())) {
            processInstanceQuery.startedAfter(DateUtils.of(pageReqVO.getCreateTime()[0]));
            processInstanceQuery.startedBefore(DateUtils.of(pageReqVO.getCreateTime()[1]));
        }
        if (ArrayUtil.isNotEmpty(pageReqVO.getEndTime())) {
            processInstanceQuery.finishedAfter(DateUtils.of(pageReqVO.getEndTime()[0]));
            processInstanceQuery.finishedBefore(DateUtils.of(pageReqVO.getEndTime()[1]));
        }
        // 表单字段查询
        Map<String, Object> formFieldsParams = JsonUtils.parseObject(pageReqVO.getFormFieldsParams(), Map.class);
        if (CollUtil.isNotEmpty(formFieldsParams)) {
            formFieldsParams.forEach((key, value) -> {
                if (StrUtil.isEmpty(String.valueOf(value))) {
                    return;
                }
                // TODO @lesan：应支持多种类型的查询方式，目前只有字符串全等
                processInstanceQuery.variableValueEquals(key, value);
            });
        }

        // 2.1 查询数量
        long processInstanceCount = processInstanceQuery.count();
        if (processInstanceCount == 0) {
            return PageResult.empty(processInstanceCount);
        }
        // 2.2 查询列表
        List<HistoricProcessInstance> processInstanceList = processInstanceQuery.listPage(PageUtils.getStart(pageReqVO),
                pageReqVO.getPageSize());
        return new PageResult<>(processInstanceList, processInstanceCount);
    }

    @Override
    public PageResult<BpmProcessInstanceFormDataRespVO> getProcessInstanceFormDataPageBySearch(BpmProcessInstanceFormDataReqVO reqVO) {
        // 1. 根据搜索条件查询流程实例（分页）
        PageResult<HistoricProcessInstance> pageResult = searchProcessInstancesByConditionWithPage(reqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }

        // 2. 获取流程定义信息
        Set<String> processDefinitionIds = convertSet(pageResult.getList(), HistoricProcessInstance::getProcessDefinitionId);
        Map<String, BpmProcessDefinitionInfoDO> processDefinitionInfoMap = processDefinitionService.getProcessDefinitionInfoMap(processDefinitionIds);

        // 3. 获取最新的流程定义信息（用于返回统一的 formFieldsInfo 和 formFields）
        BpmProcessDefinitionInfoDO latestProcessDefinitionInfo = getLatestProcessDefinitionInfo(reqVO.getProcessDefinitionKey());

        // 4. 构建表单数据列表
        List<BpmProcessInstanceFormDataRespVO> result = new ArrayList<>();
        for (HistoricProcessInstance processInstance : pageResult.getList()) {
            try {
                BpmProcessInstanceFormDataRespVO formData = buildProcessInstanceFormDataForPage(processInstance.getId(), processDefinitionInfoMap);
                if (formData != null) {
                    result.add(formData);
                }
            } catch (Exception e) {
                log.warn("获取流程实例 {} 的表单数据失败", processInstance.getId(), e);
            }
        }

        // 5. 设置统一的 formFieldsInfo 和 formFields（只返回最新版本的）
        if (latestProcessDefinitionInfo != null && CollUtil.isNotEmpty(result)) {
            Map<String, BpmProcessInstanceFormDataRespVO.FormFieldInfo> formFieldsInfo = buildFormFieldsInfo(latestProcessDefinitionInfo);
            List<String> formFields = latestProcessDefinitionInfo.getFormFields();

            // 只在第一条记录中设置 formFieldsInfo 和 formFields，其他记录设为 null
            for (int i = 0; i < result.size(); i++) {
                if (i == 0) {
                    result.get(i).setFormFieldsInfo(formFieldsInfo);
                    result.get(i).setFormFields(formFields);
                } else {
                    result.get(i).setFormFieldsInfo(null);
                    result.get(i).setFormFields(null);
                }
            }
        }

        return new PageResult<>(result, pageResult.getTotal());
    }

    /**
     * 获取最新的流程定义信息
     */
    private BpmProcessDefinitionInfoDO getLatestProcessDefinitionInfo(String processDefinitionKey) {
        try {
            // 获取最新版本的流程定义
            ProcessDefinition latestProcessDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processDefinitionKey)
                    .latestVersion()
                    .singleResult();

            if (latestProcessDefinition != null) {
                return processDefinitionService.getProcessDefinitionInfo(latestProcessDefinition.getId());
            }
        } catch (Exception e) {
            log.warn("[getLatestProcessDefinitionInfo][获取最新流程定义失败] processDefinitionKey={}", processDefinitionKey, e);
        }
        return null;
    }

    /**
     * 构建表单字段信息
     */
    private Map<String, BpmProcessInstanceFormDataRespVO.FormFieldInfo> buildFormFieldsInfo(BpmProcessDefinitionInfoDO processDefinitionInfo) {
        Map<String, BpmProcessInstanceFormDataRespVO.FormFieldInfo> formFieldsInfo = new HashMap<>();

        if (processDefinitionInfo != null && CollUtil.isNotEmpty(processDefinitionInfo.getFormFields())) {
            for (String formFieldStr : processDefinitionInfo.getFormFields()) {
                try {
                    parseComplexFormField(formFieldStr, formFieldsInfo);
                } catch (Exception e) {
                    log.warn("[buildFormFieldsInfo][解析表单字段失败] formField={}", formFieldStr, e);
                }
            }
        }

        return formFieldsInfo;
    }

    /**
     * 构建分页用的流程实例表单数据（不包含 formFieldsInfo）
     */
    private BpmProcessInstanceFormDataRespVO buildProcessInstanceFormDataForPage(String processInstanceId,
                                                                                Map<String, BpmProcessDefinitionInfoDO> processDefinitionInfoMap) {
        // 1. 查询流程实例
        HistoricProcessInstance processInstance = getHistoricProcessInstance(processInstanceId);
        if (processInstance == null) {
            return null;
        }

        // 2. 获取流程定义信息
        BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionInfoMap.get(processInstance.getProcessDefinitionId());
        if (processDefinitionInfo == null) {
            log.warn("[buildProcessInstanceFormDataForPage][流程定义信息不存在] processInstanceId={}, processDefinitionId={}",
                processInstanceId, processInstance.getProcessDefinitionId());
            return null;
        }

        // 3. 构建基本信息
        BpmProcessInstanceFormDataRespVO result = new BpmProcessInstanceFormDataRespVO();
        result.setProcessInstanceId(processInstanceId);
        result.setTitle(processInstance.getName());
        result.setStatus(FlowableUtils.getProcessInstanceStatus(processInstance));
        result.setStartTime(LocalDateTimeUtil.of(processInstance.getStartTime()));
        if (processInstance.getEndTime() != null) {
            result.setEndTime(LocalDateTimeUtil.of(processInstance.getEndTime()));
        }

        // 4. 获取表单变量
        Map<String, Object> formVariables = processInstance.getProcessVariables();
        if (formVariables == null) {
            formVariables = new HashMap<>();
        }

        // 5. 处理表单字段的选择器数据转换
        if (CollUtil.isNotEmpty(formVariables) && processDefinitionInfo != null) {
            try {
                enrichFormFieldsWithSelectorData(formVariables, processDefinitionInfo);
            } catch (Exception e) {
                log.warn("[buildProcessInstanceFormDataForPage][表单字段转换失败] processInstanceId={}", processInstanceId, e);
            }
        }

        result.setFormVariables(formVariables);
        // 注意：不设置 formFieldsInfo，这将在上层统一设置

        return result;
    }

    /**
     * 根据搜索条件查询流程实例（分页）
     *
     * @param reqVO 搜索条件
     * @return 流程实例分页结果
     */
    private PageResult<HistoricProcessInstance> searchProcessInstancesByConditionWithPage(BpmProcessInstanceFormDataReqVO reqVO) {
        // 构建查询条件
        HistoricProcessInstanceQuery processInstanceQuery = historyService.createHistoricProcessInstanceQuery()
                .includeProcessVariables()
                .processInstanceTenantId(FlowableUtils.getTenantId())
                .orderByProcessInstanceStartTime().desc();

        // 如果指定了流程实例ID列表，限制查询范围
        if (StrUtil.isNotBlank(reqVO.getProcessInstanceIds())) {
            List<String> processInstanceIds = JsonUtils.parseArray(reqVO.getProcessInstanceIds(), String.class);
            if (CollUtil.isNotEmpty(processInstanceIds)) {
                processInstanceQuery.processInstanceIds(new HashSet<>(processInstanceIds));
            }
        }

        // 流程定义Key搜索（必填）
        processInstanceQuery.processDefinitionKey(reqVO.getProcessDefinitionKey());

        // 根据流程定义配置控制查询范围
        ProcessDefinition processDefinition = processDefinitionService.getActiveProcessDefinition(reqVO.getProcessDefinitionKey());
        if (processDefinition != null) {
            BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService.getProcessDefinitionInfo(processDefinition.getId());
            if (processDefinitionInfo != null && !Boolean.TRUE.equals(processDefinitionInfo.getAllowQueryAllInstances())) {
                // 如果不允许查询所有实例，则只查询当前用户的实例
                Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
                if (currentUserId != null) {
                    processInstanceQuery.startedBy(String.valueOf(currentUserId));
                }
            }
        }

        // 状态搜索
        if (reqVO.getStatus() != null) {
            if (reqVO.getStatus().equals(BpmProcessInstanceStatusEnum.RUNNING.getStatus())) {
                // 审批中：流程实例未完成
                processInstanceQuery.unfinished();
            } else if (reqVO.getStatus().equals(BpmProcessInstanceStatusEnum.APPROVE.getStatus())) {
                // 审批通过：流程实例已完成且状态为通过
                processInstanceQuery.finished()
                        .variableValueEquals(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, BpmProcessInstanceStatusEnum.APPROVE.getStatus());
            } else if (reqVO.getStatus().equals(BpmProcessInstanceStatusEnum.REJECT.getStatus())) {
                // 审批不通过：流程实例已完成且状态为拒绝
                processInstanceQuery.finished()
                        .variableValueEquals(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, BpmProcessInstanceStatusEnum.REJECT.getStatus());
            } else if (reqVO.getStatus().equals(BpmProcessInstanceStatusEnum.CANCEL.getStatus())) {
                // 已取消：流程实例状态为取消
                processInstanceQuery.variableValueEquals(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, BpmProcessInstanceStatusEnum.CANCEL.getStatus());
            }
        }

        // 开始时间范围搜索
        if (reqVO.getStartBeginTime() != null) {
            processInstanceQuery.startedAfter(DateUtils.of(reqVO.getStartBeginTime()));
        }
        if (reqVO.getStartEndTime() != null) {
            processInstanceQuery.startedBefore(DateUtils.of(reqVO.getStartEndTime()));
        }

        // 结束时间范围搜索
        if (reqVO.getEndBeginTime() != null) {
            processInstanceQuery.finishedAfter(DateUtils.of(reqVO.getEndBeginTime()));
        }
        if (reqVO.getEndEndTime() != null) {
            processInstanceQuery.finishedBefore(DateUtils.of(reqVO.getEndEndTime()));
        }

        // 表单字段搜索 - 简单模式
        Map<String, Object> formFieldsParams = JsonUtils.parseObject(reqVO.getFormFieldsParams(), Map.class);
        if (CollUtil.isNotEmpty(formFieldsParams)) {
            formFieldsParams.forEach((key, value) -> {
                if (StrUtil.isEmpty(String.valueOf(value))) {
                    return;
                }
                processInstanceQuery.variableValueEquals(key, value);
            });
        }

        // 表单字段搜索 - 高级模式
        if (StrUtil.isNotBlank(reqVO.getFormFieldSearchList())) {
            List<BpmProcessInstanceFormFieldSearchVO> formFieldSearchList = JsonUtils.parseArray(reqVO.getFormFieldSearchList(), BpmProcessInstanceFormFieldSearchVO.class);
            if (CollUtil.isNotEmpty(formFieldSearchList)) {
                for (BpmProcessInstanceFormFieldSearchVO searchVO : formFieldSearchList) {
                    applyFormFieldSearch(processInstanceQuery, searchVO);
                }
            }
        }

        // 分页查询
        long total = processInstanceQuery.count();
        if (total == 0) {
            return PageResult.empty(total);
        }

        int offset = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
        List<HistoricProcessInstance> list = processInstanceQuery.listPage(offset, reqVO.getPageSize());

        return new PageResult<>(list, total);
    }

    /**
     * 应用表单字段搜索条件
     *
     * @param processInstanceQuery 流程实例查询对象
     * @param searchVO 搜索条件
     */
    private void applyFormFieldSearch(HistoricProcessInstanceQuery processInstanceQuery,
                                     BpmProcessInstanceFormFieldSearchVO searchVO) {
        if (searchVO == null || StrUtil.isBlank(searchVO.getFieldName()) || searchVO.getSearchType() == null) {
            return;
        }

        String fieldName = searchVO.getFieldName();
        Object searchValue = searchVO.getSearchValue();
        BpmProcessInstanceFormFieldSearchVO.SearchType searchType = searchVO.getSearchType();

        try {
            switch (searchType) {
                case EQUALS:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueEquals(fieldName, searchValue);
                    }
                    break;
                case NOT_EQUALS:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueNotEquals(fieldName, searchValue);
                    }
                    break;
                case LIKE:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueLike(fieldName, "%" + searchValue + "%");
                    }
                    break;
                case GREATER_THAN:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueGreaterThan(fieldName, searchValue);
                    }
                    break;
                case GREATER_THAN_OR_EQUAL:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueGreaterThanOrEqual(fieldName, searchValue);
                    }
                    break;
                case LESS_THAN:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueLessThan(fieldName, searchValue);
                    }
                    break;
                case LESS_THAN_OR_EQUAL:
                    if (searchValue != null) {
                        processInstanceQuery.variableValueLessThanOrEqual(fieldName, searchValue);
                    }
                    break;
                default:
                    log.warn("不支持的搜索类型: {}", searchType);
                    break;
            }
        } catch (Exception e) {
            log.warn("应用表单字段搜索条件失败: fieldName={}, searchType={}, searchValue={}",
                    fieldName, searchType, searchValue, e);
        }
    }

    /**
     * 拼接审批详情的最终数据
     * <p>
     * 主要是，拼接审批人的用户信息、部门信息
     */
    private BpmApprovalDetailRespVO buildApprovalDetail(BpmApprovalDetailReqVO reqVO,
                                                        BpmnModel bpmnModel,
                                                        ProcessDefinition processDefinition,
                                                        BpmProcessDefinitionInfoDO processDefinitionInfo,
                                                        HistoricProcessInstance processInstance,
                                                        Integer processInstanceStatus,
                                                        List<ActivityNode> endApprovalNodeInfos,
                                                        List<ActivityNode> runningApprovalNodeInfos,
                                                        List<ActivityNode> simulateApprovalNodeInfos,
                                                        BpmTaskRespVO todoTask) {
        // 1. 获取所有需要读取用户信息的 userIds
        List<ActivityNode> approveNodes = newArrayList(
                asList(endApprovalNodeInfos, runningApprovalNodeInfos, simulateApprovalNodeInfos));
        Set<Long> userIds = BpmProcessInstanceConvert.INSTANCE.parseUserIds(processInstance, approveNodes, todoTask);
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(convertSet(userMap.values(), AdminUserRespDTO::getDeptId));

        // 2. 表单权限
        String taskId = reqVO.getTaskId() == null && todoTask != null ? todoTask.getId() : reqVO.getTaskId();
        Map<String, String> formFieldsPermission = getFormFieldsPermission(bpmnModel, reqVO.getActivityId(), taskId);

        // 3. 拼接数据

        return BpmProcessInstanceConvert.INSTANCE.buildApprovalDetail(bpmnModel, processDefinition,
                processDefinitionInfo, processInstance,
                processInstanceStatus, approveNodes, todoTask, formFieldsPermission, userMap, deptMap);
    }

    /**
     * 获得【已结束】的活动节点们
     */
    private List<ActivityNode> getEndActivityNodeList(Long startUserId, BpmnModel bpmnModel,
                                                      BpmProcessDefinitionInfoDO processDefinitionInfo,
                                                      HistoricProcessInstance historicProcessInstance, Integer processInstanceStatus,
                                                      List<HistoricActivityInstance> activities, List<HistoricTaskInstance> tasks) {
        // 遍历 tasks 列表，只处理已结束的 UserTask
        // 为什么不通过 activities 呢？因为，加签场景下，它只存在于 tasks，没有 activities，导致如果遍历 activities 的话，它无法成为一个节点
        List<HistoricTaskInstance> endTasks = filterList(tasks, task -> task.getEndTime() != null);
        List<ActivityNode> approvalNodes = convertList(endTasks, task -> {
            FlowElement flowNode = BpmnModelUtils.getFlowElementById(bpmnModel, task.getTaskDefinitionKey());
            ActivityNode activityNode = new ActivityNode().setId(task.getTaskDefinitionKey()).setName(task.getName())
                    .setNodeType(START_USER_NODE_ID.equals(task.getTaskDefinitionKey())
                            ? BpmSimpleModelNodeTypeEnum.START_USER_NODE.getType()
                            : ObjUtil.defaultIfNull(parseNodeType(flowNode), // 目的：解决“办理节点”的识别
                            BpmSimpleModelNodeTypeEnum.APPROVE_NODE.getType()))
                    .setStatus(FlowableUtils.getTaskStatus(task))
                    .setCandidateStrategy(BpmnModelUtils.parseCandidateStrategy(flowNode))
                    .setStartTime(DateUtils.of(task.getCreateTime())).setEndTime(DateUtils.of(task.getEndTime()))
                    .setTasks(singletonList(BpmProcessInstanceConvert.INSTANCE.buildApprovalTaskInfo(task)));
            // 如果是取消状态，则跳过
            if (BpmTaskStatusEnum.isCancelStatus(activityNode.getStatus())) {
                return null;
            }
            return activityNode;
        });

        // 遍历 activities，只处理已结束的 StartEvent、EndEvent
        List<HistoricActivityInstance> endActivities = filterList(activities, activity -> activity.getEndTime() != null
                && (StrUtil.equalsAny(activity.getActivityType(), ELEMENT_EVENT_START, ELEMENT_CALL_ACTIVITY, ELEMENT_EVENT_END)));
        endActivities.forEach(activity -> {
            // StartEvent：只处理 BPMN 的场景。因为，SIMPLE 情况下，已经有 START_USER_NODE 节点
            if (ELEMENT_EVENT_START.equals(activity.getActivityType())
                    && BpmModelTypeEnum.BPMN.getType().equals(processDefinitionInfo.getModelType())) {
                ActivityNodeTask startTask = new ActivityNodeTask().setId(BpmnModelConstants.START_USER_NODE_ID)
                        .setAssignee(startUserId).setStatus(BpmTaskStatusEnum.APPROVE.getStatus());
                ActivityNode startNode = new ActivityNode().setId(startTask.getId())
                        .setName(BpmSimpleModelNodeTypeEnum.START_USER_NODE.getName())
                        .setNodeType(BpmSimpleModelNodeTypeEnum.START_USER_NODE.getType())
                        .setStatus(startTask.getStatus()).setTasks(ListUtil.of(startTask))
                        .setStartTime(DateUtils.of(activity.getStartTime()))
                        .setEndTime(DateUtils.of(activity.getEndTime()));
                approvalNodes.add(0, startNode);
                return;
            }
            // EndEvent
            if (ELEMENT_EVENT_END.equals(activity.getActivityType())) {
                if (BpmProcessInstanceStatusEnum.isRejectStatus(processInstanceStatus)) {
                    // 拒绝情况下，不需要展示 EndEvent 结束节点。原因是：前端已经展示 x 效果，无需重复展示
                    return;
                }
                ActivityNode endNode = new ActivityNode().setId(activity.getId())
                        .setName(BpmSimpleModelNodeTypeEnum.END_NODE.getName())
                        .setNodeType(BpmSimpleModelNodeTypeEnum.END_NODE.getType()).setStatus(processInstanceStatus)
                        .setStartTime(DateUtils.of(activity.getStartTime()))
                        .setEndTime(DateUtils.of(activity.getEndTime()));
                String reason = FlowableUtils.getProcessInstanceReason(historicProcessInstance);
                if (StrUtil.isNotEmpty(reason)) {
                    endNode.setTasks(singletonList(new ActivityNodeTask().setId(endNode.getId())
                            .setStatus(endNode.getStatus()).setReason(reason)));
                }
                approvalNodes.add(endNode);
            }
            // CallActivity
            if (ELEMENT_CALL_ACTIVITY.equals(activity.getActivityType())) {
                ActivityNode callActivity = new ActivityNode().setId(activity.getId())
                        .setName(BpmSimpleModelNodeTypeEnum.CHILD_PROCESS.getName())
                        .setNodeType(BpmSimpleModelNodeTypeEnum.CHILD_PROCESS.getType()).setStatus(processInstanceStatus)
                        .setStartTime(DateUtils.of(activity.getStartTime()))
                        .setEndTime(DateUtils.of(activity.getEndTime()))
                        .setProcessInstanceId(activity.getProcessInstanceId());
                approvalNodes.add(callActivity);
            }
        });

        // 按照时间排序
        approvalNodes.sort(Comparator.comparing(ActivityNode::getStartTime));
        return approvalNodes;
    }

    /**
     * 获得【进行中】的活动节点们
     */
    private List<ActivityNode> getRunApproveNodeList(Long startUserId,
                                                     BpmnModel bpmnModel,
                                                     ProcessDefinition processDefinition,
                                                     Map<String, Object> processVariables,
                                                     List<HistoricActivityInstance> activities,
                                                     List<HistoricTaskInstance> tasks) {
        // 构建运行中的任务、子流程，基于 activityId 分组
        List<HistoricActivityInstance> runActivities = filterList(activities, activity -> activity.getEndTime() == null
                && (StrUtil.equalsAny(activity.getActivityType(), ELEMENT_TASK_USER, ELEMENT_CALL_ACTIVITY)));
        Map<String, List<HistoricActivityInstance>> runningTaskMap = convertMultiMap(runActivities,
                HistoricActivityInstance::getActivityId);

        // 按照 activityId 分组，构建 ApprovalNodeInfo 节点
        Map<String, HistoricTaskInstance> taskMap = convertMap(tasks, HistoricTaskInstance::getId);
        return convertList(runningTaskMap.entrySet(), entry -> {
            String activityId = entry.getKey();
            List<HistoricActivityInstance> taskActivities = entry.getValue();
            // 构建活动节点
            FlowElement flowNode = BpmnModelUtils.getFlowElementById(bpmnModel, activityId);
            HistoricActivityInstance firstActivity = CollUtil.getFirst(taskActivities); // 取第一个任务，会签/或签的任务，开始时间相同
            ActivityNode activityNode = new ActivityNode().setId(firstActivity.getActivityId())
                    .setName(firstActivity.getActivityName())
                    .setNodeType(ObjUtil.defaultIfNull(parseNodeType(flowNode), // 目的：解决“办理节点”和"子流程"的识别
                            BpmSimpleModelNodeTypeEnum.APPROVE_NODE.getType()))
                    .setStatus(BpmTaskStatusEnum.RUNNING.getStatus())
                    .setCandidateStrategy(BpmnModelUtils.parseCandidateStrategy(flowNode))
                    .setStartTime(DateUtils.of(CollUtil.getFirst(taskActivities).getStartTime()))
                    .setTasks(new ArrayList<>());
            // 处理每个任务的 tasks 属性
            for (HistoricActivityInstance activity : taskActivities) {
                HistoricTaskInstance task = taskMap.get(activity.getTaskId());
                // 特殊情况：子流程节点 ChildProcess 仅存在于 activity 中，并且没有自身的 task，需要跳过执行
                // TODO @芋艿：后续看看怎么优化！
                if (task == null) {
                    continue;
                }
                activityNode.getTasks().add(BpmProcessInstanceConvert.INSTANCE.buildApprovalTaskInfo(task));
                // 加签子任务，需要过滤掉已经完成的加签子任务
                List<HistoricTaskInstance> childrenTasks = filterList(
                        taskService.getAllChildrenTaskListByParentTaskId(activity.getTaskId(), tasks),
                        childTask -> childTask.getEndTime() == null);
                if (CollUtil.isNotEmpty(childrenTasks)) {
                    activityNode.getTasks().addAll(
                            convertList(childrenTasks, BpmProcessInstanceConvert.INSTANCE::buildApprovalTaskInfo));
                }
            }
            // 处理每个任务的 candidateUsers 属性：如果是依次审批，需要预测它的后续审批人。因为 Task 是审批完一个，创建一个新的 Task
            if (BpmnModelUtils.isSequentialUserTask(flowNode)) {
                List<Long> candidateUserIds = getTaskCandidateUserList(bpmnModel, flowNode.getId(),
                        startUserId, processDefinition.getId(), processVariables);
                // 截取当前审批人位置后面的候选人，不包含当前审批人
                ActivityNodeTask approvalTaskInfo = CollUtil.getFirst(activityNode.getTasks());
                Assert.notNull(approvalTaskInfo, "任务不能为空");
                int index = CollUtil.indexOf(candidateUserIds,
                        userId -> ObjectUtils.equalsAny(userId, approvalTaskInfo.getOwner(),
                                approvalTaskInfo.getAssignee())); // 委派或者向前加签情况，需要先比较 owner
                activityNode.setCandidateUserIds(CollUtil.sub(candidateUserIds, index + 1, candidateUserIds.size()));
            }
            if (BpmSimpleModelNodeTypeEnum.CHILD_PROCESS.getType().equals(activityNode.getNodeType())) {
                activityNode.setProcessInstanceId(firstActivity.getProcessInstanceId());
            }
            return activityNode;
        });
    }

    /**
     * 获得【预测（未来）】的活动节点们
     */
    private List<ActivityNode> getSimulateApproveNodeList(Long startUserId, BpmnModel bpmnModel,
                                                          BpmProcessDefinitionInfoDO processDefinitionInfo,
                                                          Map<String, Object> processVariables,
                                                          List<HistoricActivityInstance> activities) {
        // TODO @芋艿：【可优化】在驳回场景下，未来的预测准确性不高。原因是，驳回后，HistoricActivityInstance
        // 包括了历史的操作，不是只有 startEvent 到当前节点的记录
        Set<String> runActivityIds = convertSet(activities, HistoricActivityInstance::getActivityId);
        // 情况一：BPMN 设计器
        if (Objects.equals(BpmModelTypeEnum.BPMN.getType(), processDefinitionInfo.getModelType())) {
            List<FlowElement> flowElements = BpmnModelUtils.simulateProcess(bpmnModel, processVariables);
            return convertList(flowElements, flowElement -> buildNotRunApproveNodeForBpmn(startUserId, bpmnModel,
                    processDefinitionInfo, processVariables, flowElement, runActivityIds));
        }
        // 情况二：SIMPLE 设计器
        if (Objects.equals(BpmModelTypeEnum.SIMPLE.getType(), processDefinitionInfo.getModelType())) {
            BpmSimpleModelNodeVO simpleModel = JsonUtils.parseObject(processDefinitionInfo.getSimpleModel(),
                    BpmSimpleModelNodeVO.class);
            List<BpmSimpleModelNodeVO> simpleNodes = SimpleModelUtils.simulateProcess(simpleModel, processVariables);
            return convertList(simpleNodes, simpleNode -> buildNotRunApproveNodeForSimple(startUserId, bpmnModel,
                    processDefinitionInfo, processVariables, simpleNode, runActivityIds));
        }
        throw new IllegalArgumentException("未知设计器类型：" + processDefinitionInfo.getModelType());
    }

    private ActivityNode buildNotRunApproveNodeForSimple(Long startUserId, BpmnModel bpmnModel,
                                                         BpmProcessDefinitionInfoDO processDefinitionInfo, Map<String, Object> processVariables,
                                                         BpmSimpleModelNodeVO node, Set<String> runActivityIds) {
        // TODO @芋艿：【可优化】在驳回场景下，未来的预测准确性不高。原因是，驳回后，HistoricActivityInstance
        // 包括了历史的操作，不是只有 startEvent 到当前节点的记录
        if (runActivityIds.contains(node.getId())) {
            return null;
        }

        ActivityNode activityNode = new ActivityNode().setId(node.getId()).setName(node.getName())
                .setNodeType(node.getType()).setCandidateStrategy(node.getCandidateStrategy())
                .setStatus(BpmTaskStatusEnum.NOT_START.getStatus());

        // 1. 开始节点/审批节点
        if (ObjectUtils.equalsAny(node.getType(),
                BpmSimpleModelNodeTypeEnum.START_USER_NODE.getType(),
                BpmSimpleModelNodeTypeEnum.APPROVE_NODE.getType(),
                BpmSimpleModelNodeTypeEnum.TRANSACTOR_NODE.getType())) {
            List<Long> candidateUserIds = getTaskCandidateUserList(bpmnModel, node.getId(),
                    startUserId, processDefinitionInfo.getProcessDefinitionId(), processVariables);
            activityNode.setCandidateUserIds(candidateUserIds);
            return activityNode;
        }

        // 2. 结束节点
        if (BpmSimpleModelNodeTypeEnum.END_NODE.getType().equals(node.getType())) {
            return activityNode;
        }

        // 3. 抄送节点
        if (CollUtil.isEmpty(runActivityIds) && // 流程发起时：需要展示抄送节点，用于选择抄送人
                BpmSimpleModelNodeTypeEnum.COPY_NODE.getType().equals(node.getType())) {
            List<Long> candidateUserIds = getTaskCandidateUserList(bpmnModel, node.getId(),
                    startUserId, processDefinitionInfo.getProcessDefinitionId(), processVariables);
            activityNode.setCandidateUserIds(candidateUserIds);
            return activityNode;
        }

        // 4. 子流程节点
        if (BpmSimpleModelNodeTypeEnum.CHILD_PROCESS.getType().equals(node.getType())) {
            return activityNode;
        }
        return null;
    }

    private ActivityNode buildNotRunApproveNodeForBpmn(Long startUserId, BpmnModel bpmnModel,
                                                       BpmProcessDefinitionInfoDO processDefinitionInfo, Map<String, Object> processVariables,
                                                       FlowElement node, Set<String> runActivityIds) {
        if (runActivityIds.contains(node.getId())) {
            return null;
        }
        ActivityNode activityNode = new ActivityNode().setId(node.getId())
                .setStatus(BpmTaskStatusEnum.NOT_START.getStatus());

        // 1. 开始节点
        if (node instanceof StartEvent) {
            return activityNode.setName(BpmSimpleModelNodeTypeEnum.START_USER_NODE.getName())
                    .setNodeType(BpmSimpleModelNodeTypeEnum.START_USER_NODE.getType());
        }

        // 2. 审批节点
        if (node instanceof UserTask) {
            List<Long> candidateUserIds = getTaskCandidateUserList(bpmnModel, node.getId(),
                    startUserId, processDefinitionInfo.getProcessDefinitionId(), processVariables);
            return activityNode.setName(node.getName()).setNodeType(BpmSimpleModelNodeTypeEnum.APPROVE_NODE.getType())
                    .setCandidateStrategy(BpmnModelUtils.parseCandidateStrategy(node))
                    .setCandidateUserIds(candidateUserIds);
        }

        // 3. 结束节点
        if (node instanceof EndEvent) {
            return activityNode.setName(BpmSimpleModelNodeTypeEnum.END_NODE.getName())
                    .setNodeType(BpmSimpleModelNodeTypeEnum.END_NODE.getType());
        }
        return null;
    }

    private List<Long> getTaskCandidateUserList(BpmnModel bpmnModel, String activityId,
                                                Long startUserId, String processDefinitionId, Map<String, Object> processVariables) {
        Set<Long> userIds = taskCandidateInvoker.calculateUsersByActivity(bpmnModel, activityId,
                startUserId, processDefinitionId, processVariables);
        return new ArrayList<>(userIds);
    }

    @Override
    public BpmProcessInstanceBpmnModelViewRespVO getProcessInstanceBpmnModelView(String id) {
        // 1.1 获得流程实例
        HistoricProcessInstance processInstance = getHistoricProcessInstance(id);
        if (processInstance == null) {
            return null;
        }
        // 1.2 获得流程定义
        BpmnModel bpmnModel = processDefinitionService
                .getProcessDefinitionBpmnModel(processInstance.getProcessDefinitionId());
        if (bpmnModel == null) {
            return null;
        }
        BpmSimpleModelNodeVO simpleModel = null;
        BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService.getProcessDefinitionInfo(
                processInstance.getProcessDefinitionId());
        if (processDefinitionInfo != null
                && BpmModelTypeEnum.SIMPLE.getType().equals(processDefinitionInfo.getModelType())) {
            simpleModel = JsonUtils.parseObject(processDefinitionInfo.getSimpleModel(), BpmSimpleModelNodeVO.class);
        }
        // 1.3 获得流程实例对应的活动实例列表 + 任务列表
        List<HistoricActivityInstance> activities = taskService.getActivityListByProcessInstanceId(id);
        List<HistoricTaskInstance> tasks = taskService.getTaskListByProcessInstanceId(id, true);

        // 2.1 拼接进度信息
        Set<String> unfinishedTaskActivityIds = convertSet(activities, HistoricActivityInstance::getActivityId,
                activityInstance -> activityInstance.getEndTime() == null);
        Set<String> finishedTaskActivityIds = convertSet(activities, HistoricActivityInstance::getActivityId,
                activityInstance -> activityInstance.getEndTime() != null
                        && ObjectUtil.notEqual(activityInstance.getActivityType(),
                        BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW));
        Set<String> finishedSequenceFlowActivityIds = convertSet(activities, HistoricActivityInstance::getActivityId,
                activityInstance -> activityInstance.getEndTime() != null
                        && ObjectUtil.equals(activityInstance.getActivityType(),
                        BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW));
        // 特殊：会签情况下，会有部分已完成（审批）、部分未完成（待审批），此时需要 finishedTaskActivityIds 移除掉
        finishedTaskActivityIds.removeAll(unfinishedTaskActivityIds);
        // 特殊：如果流程实例被拒绝，则需要计算是哪个活动节点。
        // 注意，只取最后一个。因为会存在多次拒绝的情况，拒绝驳回到指定节点
        Set<String> rejectTaskActivityIds = CollUtil.newHashSet();
        if (BpmProcessInstanceStatusEnum.isRejectStatus(FlowableUtils.getProcessInstanceStatus(processInstance))) {
            tasks.stream()
                    .filter(task -> BpmTaskStatusEnum.isRejectStatus(FlowableUtils.getTaskStatus(task)))
                    .max(Comparator.comparing(HistoricTaskInstance::getEndTime))
                    .ifPresent(reject -> rejectTaskActivityIds.add(reject.getTaskDefinitionKey()));
            finishedTaskActivityIds.removeAll(rejectTaskActivityIds);
        }

        // 2.2 拼接基础信息
        Set<Long> userIds = BpmProcessInstanceConvert.INSTANCE.parseUserIds02(processInstance, tasks);
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(convertSet(userMap.values(), AdminUserRespDTO::getDeptId));
        return BpmProcessInstanceConvert.INSTANCE.buildProcessInstanceBpmnModelView(processInstance, tasks, bpmnModel,
                simpleModel,
                unfinishedTaskActivityIds, finishedTaskActivityIds, finishedSequenceFlowActivityIds,
                rejectTaskActivityIds,
                userMap, deptMap);
    }

    // ========== Update 写入相关方法 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqVO createReqVO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService
                .getProcessDefinition(createReqVO.getProcessDefinitionId());
        // 发起流程
        return createProcessInstance0(userId, definition, createReqVO.getVariables(), null,
                createReqVO.getStartUserSelectAssignees());
    }

    @Override
    public String createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqDTO createReqDTO) {
        return FlowableUtils.executeAuthenticatedUserId(userId, () -> {
            // 获得流程定义
            ProcessDefinition definition = processDefinitionService
                    .getActiveProcessDefinition(createReqDTO.getProcessDefinitionKey());
            // 发起流程
            return createProcessInstance0(userId, definition, createReqDTO.getVariables(),
                    createReqDTO.getBusinessKey(),
                    createReqDTO.getStartUserSelectAssignees());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createHistoryProcessInstance(Long loginUserId, @Valid BpmProcessInstanceCreateHistoryReqVO createReqVO) {
        // 如果没有指定发起人，使用当前登录用户
        Long actualStartUserId = createReqVO.getStartUserId() != null ? createReqVO.getStartUserId() : loginUserId;
        return FlowableUtils.executeAuthenticatedUserId(actualStartUserId, () -> {
            // 1. 获得流程定义
            ProcessDefinition definition = processDefinitionService
                    .getProcessDefinition(createReqVO.getProcessDefinitionId());
            if (definition == null) {
                throw exception(PROCESS_DEFINITION_NOT_EXISTS);
            }
            if (definition.isSuspended()) {
                throw exception(PROCESS_DEFINITION_IS_SUSPENDED);
            }
            BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService
                    .getProcessDefinitionInfo(definition.getId());
            if (processDefinitionInfo == null) {
                throw exception(PROCESS_DEFINITION_NOT_EXISTS);
            }

            // 2. 准备流程变量
            Map<String, Object> variables = createReqVO.getVariables();
            if (variables == null) {
                variables = new HashMap<>();
            }
            FlowableUtils.filterProcessInstanceFormVariable(variables); // 过滤一下，避免 ProcessInstance 系统级的变量被占用
            variables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_START_USER_ID, actualStartUserId); // 设置流程变量，发起人 ID
            variables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, // 流程实例状态：审批通过
                    BpmProcessInstanceStatusEnum.APPROVE.getStatus());
            variables.put(BpmnVariableConstants.PROCESS_INSTANCE_SKIP_EXPRESSION_ENABLED, true); // 跳过表达式需要添加此变量为 true

            // 3. 创建流程实例
            ProcessInstanceBuilder processInstanceBuilder = runtimeService.createProcessInstanceBuilder()
                    .processDefinitionId(definition.getId())
                    .businessKey(createReqVO.getBusinessKey())
                    .variables(variables);

            // 3.1 设置流程名称
            if (StrUtil.isNotBlank(createReqVO.getProcessInstanceName())) {
                processInstanceBuilder.name(createReqVO.getProcessInstanceName());
            } else {
                processInstanceBuilder.name(definition.getName().trim());
            }

            // 3.2 发起流程实例
            ProcessInstance instance = processInstanceBuilder.start();
            String processInstanceId = instance.getId();

            // 4. 处理流程实例完成状态
            log.info("=== 开始处理历史流程实例完成状态 === processInstanceId={}", processInstanceId);

            // 4.1 检查流程是否已经自动完成（对于只有开始/结束事件的流程）
            ProcessInstance runningInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (runningInstance != null) {
                // 流程还在运行中，需要手动结束
                log.info("=== 流程实例还在运行，需要手动结束 === processInstanceId={}", processInstanceId);

                // 4.2 设置流程状态为审批通过
                runtimeService.setVariable(processInstanceId, BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS,
                        BpmProcessInstanceStatusEnum.APPROVE.getStatus());

                // 4.3 尝试结束流程
                moveHistoryProcessInstanceToEnd(processInstanceId, definition.getId(), "历史数据同步，直接完成");

                // 4.4 再次检查是否完成
                runningInstance = runtimeService.createProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();
                if (runningInstance != null) {
                    // 如果还在运行，强制删除
                    log.warn("=== 流程实例仍在运行，强制删除 === processInstanceId={}", processInstanceId);
                    runtimeService.deleteProcessInstance(processInstanceId, "历史数据同步，强制完成");
                }
            } else {
                // 流程已经自动完成（常见于只有开始/结束事件的流程）
                log.info("=== 流程实例已自动完成 === processInstanceId={}", processInstanceId);

                // 确保历史记录中的状态正确
                try {
                    // 通过历史服务更新状态变量
                    String updateSql = "UPDATE ACT_HI_VARINST SET TEXT_ = ? WHERE PROC_INST_ID_ = ? AND NAME_ = ?";
                    jdbcTemplate.update(updateSql,
                        String.valueOf(BpmProcessInstanceStatusEnum.APPROVE.getStatus()),
                        processInstanceId,
                        BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS);
                } catch (Exception e) {
                    log.warn("=== 更新历史状态变量失败，但不影响主流程 === processInstanceId={}", processInstanceId, e);
                }
            }

            // 5. 更新历史流程实例的开始和结束时间
            updateHistoricProcessInstanceTimes(processInstanceId, createReqVO.getStartTime(), createReqVO.getEndTime());

            return processInstanceId;
        });
    }

    /**
     * 专门为历史流程实例设计的结束方法，处理没有审批节点的情况
     *
     * @param processInstanceId 流程实例ID
     * @param processDefinitionId 流程定义ID
     * @param reason 结束原因
     */
    private void moveHistoryProcessInstanceToEnd(String processInstanceId, String processDefinitionId, String reason) {
        try {
            // 1. 获取当前运行的任务列表
            List<Task> taskList = taskService.getRunningTaskListByProcessInstanceId(processInstanceId, null, null);

            if (CollUtil.isNotEmpty(taskList)) {
                // 情况一：有运行中的任务，使用原有逻辑
                taskService.moveTaskToEnd(processInstanceId, reason);
            } else {
                // 情况二：没有运行中的任务（只有开始事件和结束事件），直接处理流程实例
                log.info("[moveHistoryProcessInstanceToEnd][流程没有审批节点，直接处理流程实例] processInstanceId={}", processInstanceId);

                // 2.1 获取BPMN模型
                BpmnModel bpmnModel = processDefinitionService.getProcessDefinitionBpmnModel(processDefinitionId);
                if (bpmnModel == null) {
                    log.warn("[moveHistoryProcessInstanceToEnd][无法获取BPMN模型] processDefinitionId={}", processDefinitionId);
                    return;
                }

                // 2.2 获取结束事件
                EndEvent endEvent = BpmnModelUtils.getEndEvent(bpmnModel);
                if (endEvent == null) {
                    log.warn("[moveHistoryProcessInstanceToEnd][无法找到结束事件] processDefinitionId={}", processDefinitionId);
                    return;
                }

                // 2.3 检查流程是否还在运行
                ProcessInstance runningInstance = runtimeService.createProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (runningInstance != null) {
                    // 2.4 获取当前活动的执行实例
                    List<Execution> executions = runtimeService.createExecutionQuery()
                            .processInstanceId(processInstanceId)
                            .list();

                    if (CollUtil.isNotEmpty(executions)) {
                        // 尝试将执行实例移动到结束事件
                        try {
                            List<String> executionIds = convertList(executions, Execution::getId);
                            runtimeService.createChangeActivityStateBuilder()
                                    .processInstanceId(processInstanceId)
                                    .moveExecutionsToSingleActivityId(executionIds, endEvent.getId())
                                    .changeState();
                        } catch (Exception e) {
                            log.warn("[moveHistoryProcessInstanceToEnd][移动执行实例到结束事件失败，将强制删除流程实例] processInstanceId={}, error={}",
                                    processInstanceId, e.getMessage());
                            // 如果移动失败，直接删除流程实例
                            runtimeService.deleteProcessInstance(processInstanceId, reason);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[moveHistoryProcessInstanceToEnd][处理历史流程实例结束失败] processInstanceId={}, reason={}",
                    processInstanceId, reason, e);
            // 最后的兜底方案：直接删除流程实例
            try {
                ProcessInstance runningInstance = runtimeService.createProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();
                if (runningInstance != null) {
                    runtimeService.deleteProcessInstance(processInstanceId, reason + " - 兜底删除");
                }
            } catch (Exception deleteEx) {
                log.error("[moveHistoryProcessInstanceToEnd][兜底删除流程实例也失败] processInstanceId={}", processInstanceId, deleteEx);
            }
        }
    }

    /**
     * 更新历史流程实例的开始和结束时间
     *
     * @param processInstanceId 流程实例ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void updateHistoricProcessInstanceTimes(String processInstanceId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 计算持续时间（毫秒）
            long duration = java.time.Duration.between(startTime, endTime).toMillis();

            // 更新 ACT_HI_PROCINST 表的时间字段，确保 END_TIME_ 不为空（表示已完成）
            String updateSql = "UPDATE ACT_HI_PROCINST SET START_TIME_ = ?, END_TIME_ = ?, DURATION_ = ? WHERE ID_ = ?";
            jdbcTemplate.update(updateSql,
                java.sql.Timestamp.valueOf(startTime),
                java.sql.Timestamp.valueOf(endTime),
                duration,
                processInstanceId);

            log.info("[updateHistoricProcessInstanceTimes][更新流程实例时间成功] processInstanceId={}, startTime={}, endTime={}, duration={}ms",
                processInstanceId, startTime, endTime, duration);
        } catch (Exception e) {
            log.error("[updateHistoricProcessInstanceTimes][更新流程实例时间失败] processInstanceId={}, startTime={}, endTime={}",
                processInstanceId, startTime, endTime, e);
            // 不抛出异常，避免影响主流程
        }
    }

    private String createProcessInstance0(Long userId, ProcessDefinition definition,
                                          Map<String, Object> variables, String businessKey,
                                          Map<String, List<Long>> startUserSelectAssignees) {
        // 1.1 校验流程定义
        if (definition == null) {
            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
        }
        if (definition.isSuspended()) {
            throw exception(PROCESS_DEFINITION_IS_SUSPENDED);
        }
        BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService
                .getProcessDefinitionInfo(definition.getId());
        if (processDefinitionInfo == null) {
            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
        }
        // 1.2 校验是否能够发起
        if (!processDefinitionService.canUserStartProcessDefinition(processDefinitionInfo, userId)) {
            throw exception(PROCESS_INSTANCE_START_USER_CAN_START);
        }
        // 1.3 校验发起人自选审批人
        validateStartUserSelectAssignees(userId, definition, startUserSelectAssignees, variables);

        // 2. 创建流程实例
        if (variables == null) {
            variables = new HashMap<>();
        }
        FlowableUtils.filterProcessInstanceFormVariable(variables); // 过滤一下，避免 ProcessInstance 系统级的变量被占用
        variables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_START_USER_ID, userId); // 设置流程变量，发起人 ID
        variables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS, // 流程实例状态：审批中
                BpmProcessInstanceStatusEnum.RUNNING.getStatus());
        variables.put(BpmnVariableConstants.PROCESS_INSTANCE_SKIP_EXPRESSION_ENABLED, true); // 跳过表达式需要添加此变量为 true，不影响没配置 skipExpression 的节点
        if (CollUtil.isNotEmpty(startUserSelectAssignees)) {
            // 设置流程变量，发起人自选审批人
            variables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_START_USER_SELECT_ASSIGNEES,
                    startUserSelectAssignees);
        }

        // 3. 创建流程
        ProcessInstanceBuilder processInstanceBuilder = runtimeService.createProcessInstanceBuilder()
                .processDefinitionId(definition.getId())
                .businessKey(businessKey)
                .variables(variables);
        // 3.1 创建流程 ID
        BpmModelMetaInfoVO.ProcessIdRule processIdRule = processDefinitionInfo.getProcessIdRule();
        if (processIdRule != null && Boolean.TRUE.equals(processIdRule.getEnable())) {
            processInstanceBuilder.predefineProcessInstanceId(processIdRedisDAO.generate(processIdRule));
        }
        // 3.2 流程名称
        BpmModelMetaInfoVO.TitleSetting titleSetting = processDefinitionInfo.getTitleSetting();
        if (titleSetting != null && Boolean.TRUE.equals(titleSetting.getEnable())) {
            AdminUserRespDTO user = adminUserApi.getUser(userId);
            Map<String, Object> cloneVariables = new HashMap<>(variables);
            cloneVariables.put(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_START_USER_ID, user.getNickname());
            cloneVariables.put(BpmnVariableConstants.PROCESS_START_TIME, DateUtil.now());
            cloneVariables.put(BpmnVariableConstants.PROCESS_DEFINITION_NAME, definition.getName().trim());
            processInstanceBuilder.name(StrUtil.format(titleSetting.getTitle(), cloneVariables));
        } else {
            processInstanceBuilder.name(definition.getName().trim());
        }
        // 3.3 发起流程实例
        ProcessInstance instance = processInstanceBuilder.start();
        return instance.getId();
    }

    private void validateStartUserSelectAssignees(Long userId, ProcessDefinition definition,
                                                  Map<String, List<Long>> startUserSelectAssignees,
                                                  Map<String, Object> variables) {
        // 1. 获取预测的节点信息
        BpmApprovalDetailRespVO detailRespVO = getApprovalDetail(userId, new BpmApprovalDetailReqVO()
                .setProcessDefinitionId(definition.getId())
                .setProcessVariables(variables));
        List<ActivityNode> activityNodes = detailRespVO.getActivityNodes();
        if (CollUtil.isEmpty(activityNodes)) {
            return;
        }

        // 2.1 移除掉不是发起人自选审批人节点
        activityNodes.removeIf(task ->
                ObjectUtil.notEqual(BpmTaskCandidateStrategyEnum.START_USER_SELECT.getStrategy(), task.getCandidateStrategy()));
        // 2.2 流程发起时要先获取当前流程的预测走向节点，发起时只校验预测的节点发起人自选审批人的审批人和抄送人是否都配置了
        activityNodes.forEach(task -> {
            List<Long> assignees = startUserSelectAssignees != null ? startUserSelectAssignees.get(task.getId()) : null;
            if (CollUtil.isEmpty(assignees)) {
                throw exception(PROCESS_INSTANCE_START_USER_SELECT_ASSIGNEES_NOT_CONFIG, task.getName());
            }
            Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(assignees);
            assignees.forEach(assignee -> {
                if (userMap.get(assignee) == null) {
                    throw exception(PROCESS_INSTANCE_START_USER_SELECT_ASSIGNEES_NOT_EXISTS, task.getName(), assignee);
                }
            });
        });
    }

    @Override
    public void cancelProcessInstanceByStartUser(Long userId, @Valid BpmProcessInstanceCancelReqVO cancelReqVO) {
        // 1.1 校验流程实例存在
        ProcessInstance instance = getProcessInstance(cancelReqVO.getId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        // 1.2 只能取消自己的
        if (!Objects.equals(instance.getStartUserId(), String.valueOf(userId))) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
        }
        // 1.3 校验允许撤销审批中的申请
        BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService
                .getProcessDefinitionInfo(instance.getProcessDefinitionId());
        Assert.notNull(processDefinitionInfo, "流程定义({})不存在", processDefinitionInfo);
        if (processDefinitionInfo.getAllowCancelRunningProcess() != null // 防止未配置 AllowCancelRunningProcess , 默认为可取消
                && !processDefinitionInfo.getAllowCancelRunningProcess()) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_ALLOW);
        }
        // 1.4 子流程不允许取消
        if (StrUtil.isNotBlank(instance.getSuperExecutionId())) {
            throw exception(PROCESS_INSTANCE_CANCEL_CHILD_FAIL_NOT_ALLOW);
        }

        // 2. 取消流程
        updateProcessInstanceCancel(cancelReqVO.getId(),
                BpmReasonEnum.CANCEL_PROCESS_INSTANCE_BY_START_USER.format(cancelReqVO.getReason()));
    }

    @Override
    public void cancelProcessInstanceByAdmin(Long userId, BpmProcessInstanceCancelReqVO cancelReqVO) {
        // 1.1 校验流程实例存在
        ProcessInstance instance = getProcessInstance(cancelReqVO.getId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }

        // 2. 取消流程
        AdminUserRespDTO user = adminUserApi.getUser(userId);
        updateProcessInstanceCancel(cancelReqVO.getId(),
                BpmReasonEnum.CANCEL_PROCESS_INSTANCE_BY_ADMIN.format(user.getNickname(), cancelReqVO.getReason()));
    }

    private void updateProcessInstanceCancel(String id, String reason) {
        // 1. 更新流程实例 status
        runtimeService.setVariable(id, BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS,
                BpmProcessInstanceStatusEnum.CANCEL.getStatus());
        runtimeService.setVariable(id, BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_REASON, reason);

        // 2. 取消所有子流程
        List<ProcessInstance> childProcessInstances = runtimeService.createProcessInstanceQuery()
                .superProcessInstanceId(id).list();
        childProcessInstances.forEach(processInstance -> updateProcessInstanceCancel(
                processInstance.getProcessInstanceId(), BpmReasonEnum.CANCEL_CHILD_PROCESS_INSTANCE_BY_MAIN_PROCESS.getReason()));

        // 3. 结束流程
        taskService.moveTaskToEnd(id, reason);
    }

    @Override
    public void updateProcessInstanceReject(ProcessInstance processInstance, String reason) {
        runtimeService.setVariable(processInstance.getProcessInstanceId(),
                BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS,
                BpmProcessInstanceStatusEnum.REJECT.getStatus());
        runtimeService.setVariable(processInstance.getProcessInstanceId(),
                BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_REASON,
                BpmReasonEnum.REJECT_TASK.format(reason));
    }

    @Override
    public void updateProcessInstanceVariables(String id, Map<String, Object> variables) {
        runtimeService.setVariables(id, variables);
    }

    @Override
    public void removeProcessInstanceVariables(String id, Collection<String> variableNames) {
        runtimeService.removeVariables(id, variableNames);
    }

    // ========== Event 事件相关方法 ==========

    @Override
    public void processProcessInstanceCompleted(ProcessInstance instance) {
        // 注意：需要基于 instance 设置租户编号，避免 Flowable 内部异步时，丢失租户编号
        FlowableUtils.execute(instance.getTenantId(), () -> {
            // 1.1 获取当前状态
            Integer status = (Integer) instance.getProcessVariables()
                    .get(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS);
            String reason = (String) instance.getProcessVariables()
                    .get(BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_REASON);
            // 1.2 当流程状态还是审批状态中，说明审批通过了，则变更下它的状态
            // 为什么这么处理？因为流程完成，并且完成了，说明审批通过了
            if (Objects.equals(status, BpmProcessInstanceStatusEnum.RUNNING.getStatus())) {
                status = BpmProcessInstanceStatusEnum.APPROVE.getStatus();
                runtimeService.setVariable(instance.getId(), BpmnVariableConstants.PROCESS_INSTANCE_VARIABLE_STATUS,
                        status);
            }

            // 2. 发送对应的消息通知
            if (Objects.equals(status, BpmProcessInstanceStatusEnum.APPROVE.getStatus())) {
                messageService.sendMessageWhenProcessInstanceApprove(
                        BpmProcessInstanceConvert.INSTANCE.buildProcessInstanceApproveMessage(instance));
            } else if (Objects.equals(status, BpmProcessInstanceStatusEnum.REJECT.getStatus())) {
                messageService.sendMessageWhenProcessInstanceReject(
                        BpmProcessInstanceConvert.INSTANCE.buildProcessInstanceRejectMessage(instance, reason));
            }

            // 3. 发送流程实例的状态事件
            processInstanceEventPublisher.sendProcessInstanceResultEvent(
                    BpmProcessInstanceConvert.INSTANCE.buildProcessInstanceStatusEvent(this, instance, status));

            // 4. 流程后置通知
            if (Objects.equals(status, BpmProcessInstanceStatusEnum.APPROVE.getStatus())) {
                BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService.
                        getProcessDefinitionInfo(instance.getProcessDefinitionId());
                if (ObjUtil.isNotNull(processDefinitionInfo) &&
                        ObjUtil.isNotNull(processDefinitionInfo.getProcessAfterTriggerSetting())) {
                    BpmModelMetaInfoVO.HttpRequestSetting setting = processDefinitionInfo.getProcessAfterTriggerSetting();

                    BpmHttpRequestUtils.executeBpmHttpRequest(instance,
                            setting.getUrl(), setting.getHeader(), setting.getBody(), true, setting.getResponse());
                }
            }
        });
    }

    @Override
    public void processProcessInstanceCreated(ProcessInstance instance) {
        // 注意：需要基于 instance 设置租户编号，避免 Flowable 内部异步时，丢失租户编号
        FlowableUtils.execute(instance.getTenantId(), () -> {
            // 流程前置通知
            BpmProcessDefinitionInfoDO processDefinitionInfo = processDefinitionService.
                    getProcessDefinitionInfo(instance.getProcessDefinitionId());
            if (ObjUtil.isNull(processDefinitionInfo) ||
                    ObjUtil.isNull(processDefinitionInfo.getProcessBeforeTriggerSetting())) {
                return;
            }
            BpmModelMetaInfoVO.HttpRequestSetting setting = processDefinitionInfo.getProcessBeforeTriggerSetting();
            BpmHttpRequestUtils.executeBpmHttpRequest(instance,
                    setting.getUrl(), setting.getHeader(), setting.getBody(), true, setting.getResponse());
        });
    }

    /**
     * 解析复杂的表单字段结构
     *
     * @param formFieldStr 表单字段JSON字符串
     * @param formFieldsInfo 表单字段信息Map
     */
    private void parseComplexFormField(String formFieldStr, Map<String, BpmProcessInstanceFormDataRespVO.FormFieldInfo> formFieldsInfo) {
        try {
            Map<String, Object> fieldMap = JsonUtils.parseObject(formFieldStr, Map.class);
            if (fieldMap != null) {
                extractFieldsFromMap(fieldMap, formFieldsInfo);
            }
        } catch (Exception e) {
            log.warn("解析复杂表单字段失败: {}", formFieldStr, e);
        }
    }

    /**
     * 从Map中递归提取表单字段信息
     *
     * @param fieldMap 字段Map
     * @param formFieldsInfo 表单字段信息Map
     */
    @SuppressWarnings("unchecked")
    private void extractFieldsFromMap(Map<String, Object> fieldMap, Map<String, BpmProcessInstanceFormDataRespVO.FormFieldInfo> formFieldsInfo) {
        // 检查当前层级是否有field和title
        String field = (String) fieldMap.get("field");
        String title = (String) fieldMap.get("title");
        String type = (String) fieldMap.get("type");

        if (StrUtil.isNotBlank(field) && StrUtil.isNotBlank(title)) {
            BpmProcessInstanceFormDataRespVO.FormFieldInfo fieldInfo = buildCompleteFieldInfo(fieldMap);
            formFieldsInfo.put(field, fieldInfo);
            log.info("提取到表单字段: {} -> {} (类型: {})", field, title, type);
        }

        // 递归处理children数组
        Object children = fieldMap.get("children");
        if (children instanceof List) {
            List<Object> childrenList = (List<Object>) children;
            for (Object child : childrenList) {
                if (child instanceof Map) {
                    extractFieldsFromMap((Map<String, Object>) child, formFieldsInfo);
                }
            }
        }

        // 递归处理props.rule数组（用于group类型字段）
        Object props = fieldMap.get("props");
        if (props instanceof Map) {
            Map<String, Object> propsMap = (Map<String, Object>) props;
            Object rule = propsMap.get("rule");
            if (rule instanceof List) {
                List<Object> ruleList = (List<Object>) rule;
                for (Object ruleItem : ruleList) {
                    if (ruleItem instanceof Map) {
                        extractFieldsFromMap((Map<String, Object>) ruleItem, formFieldsInfo);
                    }
                }
            }
        }
    }

    /**
     * 构建完整的表单字段信息，包含嵌套结构
     *
     * @param fieldMap 字段Map
     * @return 完整的字段信息
     */
    @SuppressWarnings("unchecked")
    private BpmProcessInstanceFormDataRespVO.FormFieldInfo buildCompleteFieldInfo(Map<String, Object> fieldMap) {
        BpmProcessInstanceFormDataRespVO.FormFieldInfo fieldInfo = new BpmProcessInstanceFormDataRespVO.FormFieldInfo();

        // 设置基本字段信息
        fieldInfo.setField((String) fieldMap.get("field"));
        fieldInfo.setLabel((String) fieldMap.get("title"));
        fieldInfo.setType((String) fieldMap.get("type"));

        // 处理props属性
        Object props = fieldMap.get("props");
        if (props instanceof Map) {
            fieldInfo.setProps((Map<String, Object>) props);
        }

        // 处理options选项
        Object options = fieldMap.get("options");
        if (options instanceof List) {
            fieldInfo.setOptions((List<Map<String, Object>>) options);
        }

        // 处理children子字段
        Object children = fieldMap.get("children");
        if (children instanceof List) {
            List<Object> childrenList = (List<Object>) children;
            List<BpmProcessInstanceFormDataRespVO.FormFieldInfo> childFieldInfos = new ArrayList<>();
            for (Object child : childrenList) {
                if (child instanceof Map) {
                    Map<String, Object> childMap = (Map<String, Object>) child;
                    String childField = (String) childMap.get("field");
                    String childTitle = (String) childMap.get("title");
                    if (StrUtil.isNotBlank(childField) && StrUtil.isNotBlank(childTitle)) {
                        BpmProcessInstanceFormDataRespVO.FormFieldInfo childFieldInfo = buildCompleteFieldInfo(childMap);
                        childFieldInfos.add(childFieldInfo);
                    }
                }
            }
            if (!childFieldInfos.isEmpty()) {
                fieldInfo.setChildren(childFieldInfos);
            }
        }

        // 处理props.rule规则字段（用于group类型字段）
        if (props instanceof Map) {
            Map<String, Object> propsMap = (Map<String, Object>) props;
            Object rule = propsMap.get("rule");
            if (rule instanceof List) {
                List<Object> ruleList = (List<Object>) rule;
                List<BpmProcessInstanceFormDataRespVO.FormFieldInfo> ruleFieldInfos = new ArrayList<>();
                for (Object ruleItem : ruleList) {
                    if (ruleItem instanceof Map) {
                        Map<String, Object> ruleMap = (Map<String, Object>) ruleItem;
                        String ruleField = (String) ruleMap.get("field");
                        String ruleTitle = (String) ruleMap.get("title");
                        if (StrUtil.isNotBlank(ruleField) && StrUtil.isNotBlank(ruleTitle)) {
                            BpmProcessInstanceFormDataRespVO.FormFieldInfo ruleFieldInfo = buildCompleteFieldInfo(ruleMap);
                            ruleFieldInfos.add(ruleFieldInfo);
                        }
                    }
                }
                if (!ruleFieldInfos.isEmpty()) {
                    fieldInfo.setRules(ruleFieldInfos);
                }
            }
        }

        return fieldInfo;
    }

    /**
     * 丰富表单字段的选择器数据，将ID转换为用户信息
     *
     * @param formVariables 表单变量
     * @param processDefinitionInfo 流程定义信息
     */
    public void enrichFormFieldsWithSelectorData(Map<String, Object> formVariables,
                                                 BpmProcessDefinitionInfoDO processDefinitionInfo) {
        if (CollUtil.isEmpty(formVariables) || processDefinitionInfo == null ||
            CollUtil.isEmpty(processDefinitionInfo.getFormFields())) {
            log.debug("[enrichFormFieldsWithSelectorData][跳过处理] formVariables={}, processDefinitionInfo={}, formFields={}",
                    CollUtil.isEmpty(formVariables), processDefinitionInfo == null,
                    processDefinitionInfo != null ? CollUtil.isEmpty(processDefinitionInfo.getFormFields()) : "null");
            return;
        }

        try {
            // 解析表单字段定义
            Map<String, String> fieldTypeMap = new HashMap<>();
            Map<String, Map<String, Object>> fieldOptionsMap = new HashMap<>(); // 存储字段选项信息
            for (String formFieldStr : processDefinitionInfo.getFormFields()) {
                Map<String, Object> fieldMap = JsonUtils.parseObject(formFieldStr, Map.class);
                if (fieldMap != null) {
                    extractFieldTypes(fieldMap, fieldTypeMap);
                    extractFieldOptions(fieldMap, fieldOptionsMap);
                }
            }

            // 使用新的Map来存储转换后的字段，避免ConcurrentModificationException
            Map<String, Object> displayFields = new HashMap<>();

            for (Map.Entry<String, Object> entry : new HashMap<>(formVariables).entrySet()) {
                String fieldName = entry.getKey();
                Object fieldValue = entry.getValue();
                String fieldType = fieldTypeMap.get(fieldName);

                if (StrUtil.isNotBlank(fieldType) && fieldValue != null) {
                    Object enrichedValue;

                    // 只处理指定的8种类型
                    switch (fieldType) {
                        case "UserSelect":
                        case "CurrentUserSelect":
                            enrichedValue = convertUserSelectValue(fieldValue);
                            break;

                        case "DeptSelect":
                        case "CurrentUserDeptSelect":
                            enrichedValue = convertDeptSelectValue(fieldValue);
                            break;

                        case "ProcessSelect":
                        case "ProcessSelects":
                        case "ContractSelect": // 保持向后兼容
                            enrichedValue = convertProcessSelectValue(fieldValue);
                            break;

                        case "DictSelect":
                            // 字典选择器需要字典类型信息
                            String dictType = fieldTypeMap.get(fieldName + "_dictType");
                            enrichedValue = convertDictSelectValue(fieldValue, dictType);
                            break;

                        case "group":
                            // 设置字段类型映射上下文，然后进行组嵌套转换
                            setCurrentFieldTypeMap(fieldTypeMap);
                            try {
                                enrichedValue = convertGroupValue(fieldValue, fieldOptionsMap);
                            } finally {
                                clearCurrentFieldTypeMap();
                            }
                            break;

                        default:
                            continue;
                    }

                    if (enrichedValue != null && !enrichedValue.equals(fieldValue)) {
                        // 添加转换后的字段到临时Map中，使用 _detail 后缀
                        displayFields.put(fieldName + "_detail", enrichedValue);
                    }
                }
            }
            formVariables.putAll(displayFields);
        } catch (Exception e) {
            log.warn("[enrichFormFieldsWithSelectorData][丰富表单字段选择器数据失败]", e);
        }
    }

    /**
     * 递归提取字段类型映射
     *
     * @param fieldMap 字段Map
     * @param fieldTypeMap 字段类型映射
     */
    @SuppressWarnings("unchecked")
    private void extractFieldTypes(Map<String, Object> fieldMap, Map<String, String> fieldTypeMap) {
        String field = (String) fieldMap.get("field");
        String type = (String) fieldMap.get("type");

        // 提取当前层级的字段信息
        if (StrUtil.isNotBlank(field) && StrUtil.isNotBlank(type)) {
            fieldTypeMap.put(field, type);

            // 特殊处理DictSelect类型，提取字典类型信息
            if ("DictSelect".equals(type)) {
                Object props = fieldMap.get("props");
                if (props instanceof Map) {
                    Map<String, Object> propsMap = (Map<String, Object>) props;
                    String dictType = (String) propsMap.get("dictType");
                    if (StrUtil.isNotBlank(dictType)) {
                        // 将字典类型信息存储到字段类型映射中
                        fieldTypeMap.put(field + "_dictType", dictType);
                    }
                }
            }
        }

        // 递归处理children数组
        Object children = fieldMap.get("children");
        if (children instanceof List) {
            List<Object> childrenList = (List<Object>) children;
            for (Object child : childrenList) {
                if (child instanceof Map) {
                    extractFieldTypes((Map<String, Object>) child, fieldTypeMap);
                }
            }
        }

        // 递归处理props.rule数组（用于group类型字段）
        Object props = fieldMap.get("props");
        if (props instanceof Map) {
            Map<String, Object> propsMap = (Map<String, Object>) props;
            Object rule = propsMap.get("rule");
            if (rule instanceof List) {
                List<Object> ruleList = (List<Object>) rule;
                for (Object ruleItem : ruleList) {
                    if (ruleItem instanceof Map) {
                        extractFieldTypes((Map<String, Object>) ruleItem, fieldTypeMap);
                    }
                }
            }

            // 特殊处理 tableForm 类型
            if ("tableForm".equals(type)) {
                extractTableFormFieldTypes(propsMap, fieldTypeMap);
            }
        }
    }

    /**
     * 提取 tableForm 中的字段类型信息
     */
    @SuppressWarnings("unchecked")
    private void extractTableFormFieldTypes(Map<String, Object> propsMap, Map<String, String> fieldTypeMap) {
        Object columns = propsMap.get("columns");
        if (columns instanceof List) {
            List<Object> columnsList = (List<Object>) columns;
            for (Object column : columnsList) {
                if (column instanceof Map) {
                    Map<String, Object> columnMap = (Map<String, Object>) column;
                    Object rule = columnMap.get("rule");
                    if (rule instanceof List) {
                        List<Object> ruleList = (List<Object>) rule;
                        for (Object ruleItem : ruleList) {
                            if (ruleItem instanceof Map) {
                                Map<String, Object> ruleItemMap = (Map<String, Object>) ruleItem;
                                String fieldName = (String) ruleItemMap.get("field");
                                String fieldType = (String) ruleItemMap.get("type");

                                if (StrUtil.isNotBlank(fieldName) && StrUtil.isNotBlank(fieldType)) {
                                    fieldTypeMap.put(fieldName, fieldType);

                                    // 如果是字典选择器，提取字典类型
                                    if ("DictSelect".equals(fieldType)) {
                                        Object ruleProps = ruleItemMap.get("props");
                                        if (ruleProps instanceof Map) {
                                            Map<String, Object> rulePropsMap = (Map<String, Object>) ruleProps;
                                            String dictType = (String) rulePropsMap.get("dictType");
                                            if (StrUtil.isNotBlank(dictType)) {
                                                fieldTypeMap.put(fieldName + "_dictType", dictType);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 递归提取字段选项信息
     *
     * @param fieldMap 字段Map
     * @param fieldOptionsMap 字段选项映射
     */
    @SuppressWarnings("unchecked")
    private void extractFieldOptions(Map<String, Object> fieldMap, Map<String, Map<String, Object>> fieldOptionsMap) {
        String field = (String) fieldMap.get("field");
        String type = (String) fieldMap.get("type");

        if (StrUtil.isNotBlank(field) && StrUtil.isNotBlank(type)) {
            // 提取选项信息（checkbox、select等类型）
            Object options = fieldMap.get("options");
            if (options instanceof List) {
                Map<String, Object> fieldInfo = new HashMap<>();
                fieldInfo.put("type", type);
                fieldInfo.put("options", options);
                fieldOptionsMap.put(field, fieldInfo);
            }
        }

        // 递归处理children数组
        Object children = fieldMap.get("children");
        if (children instanceof List) {
            List<Object> childrenList = (List<Object>) children;
            for (Object child : childrenList) {
                if (child instanceof Map) {
                    extractFieldOptions((Map<String, Object>) child, fieldOptionsMap);
                }
            }
        }
    }

    /**
     * 转换用户选择器值 - 返回完整用户信息对象
     */
    private Object convertUserSelectValue(Object fieldValue) {
        if (fieldValue instanceof Number) {
            Long userId = ((Number) fieldValue).longValue();
            AdminUserRespDTO user = adminUserApi.getUser(userId);
            return user != null ? buildUserDetailObject(user) : null;
        } else if (fieldValue instanceof String) {
            try {
                Long userId = Long.parseLong((String) fieldValue);
                AdminUserRespDTO user = adminUserApi.getUser(userId);
                return user != null ? buildUserDetailObject(user) : null;
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (fieldValue instanceof List<?> userIds) {
            List<Map<String, Object>> userDetails = new ArrayList<>();
            for (Object userId : userIds) {
                if (userId instanceof Number) {
                    AdminUserRespDTO user = adminUserApi.getUser(((Number) userId).longValue());
                    if (user != null) {
                        userDetails.add(buildUserDetailObject(user));
                    }
                }
            }
            return userDetails.isEmpty() ? null : userDetails;
        }
        return null;
    }

    /**
     * 构建用户详情对象
     */
    private Map<String, Object> buildUserDetailObject(AdminUserRespDTO user) {
        Map<String, Object> userDetail = new HashMap<>();
        userDetail.put("id", user.getId());
        userDetail.put("nickname", user.getNickname());
        userDetail.put("avatar", user.getAvatar());
        userDetail.put("mobile", user.getMobile());
        userDetail.put("deptId", user.getDeptId());
        userDetail.put("postIds", user.getPostIds());
        userDetail.put("status", user.getStatus());

        return userDetail;
    }

    /**
     * 转换部门选择器值 - 返回完整部门信息对象
     */
    private Object convertDeptSelectValue(Object fieldValue) {
        if (fieldValue instanceof Number) {
            Long deptId = ((Number) fieldValue).longValue();
            DeptRespDTO dept = deptApi.getDept(deptId);
            return dept != null ? buildDeptDetailObject(dept) : null;
        } else if (fieldValue instanceof String) {
            try {
                Long deptId = Long.parseLong((String) fieldValue);
                DeptRespDTO dept = deptApi.getDept(deptId);
                return dept != null ? buildDeptDetailObject(dept) : null;
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (fieldValue instanceof List<?> deptIds) {
            List<Map<String, Object>> deptDetails = new ArrayList<>();
            for (Object deptId : deptIds) {
                if (deptId instanceof Number) {
                    DeptRespDTO dept = deptApi.getDept(((Number) deptId).longValue());
                    if (dept != null) {
                        deptDetails.add(buildDeptDetailObject(dept));
                    }
                }
            }
            return deptDetails.isEmpty() ? null : deptDetails;
        }
        return null;
    }

    /**
     * 构建部门详情对象
     */
    private Map<String, Object> buildDeptDetailObject(DeptRespDTO dept) {
        Map<String, Object> deptDetail = new HashMap<>();
        deptDetail.put("id", dept.getId());
        deptDetail.put("name", dept.getName());
        deptDetail.put("parentId", dept.getParentId());
        deptDetail.put("leaderUserId", dept.getLeaderUserId());
        deptDetail.put("status", dept.getStatus());

        return deptDetail;
    }

    /**
     * 转换字典选择器值 - 返回字典详情对象
     * 通过 dictType 和 value 查询 system_dict_data 表
     */
    private Object convertDictSelectValue(Object fieldValue, String dictType) {
        if (StrUtil.isBlank(dictType)) {
            // 没有字典类型信息，返回简单对象
            return buildSimpleDictObject(fieldValue, null);
        }

        try {
            if (fieldValue instanceof String dictValue) {
                return buildDictDetailObject(dictValue, dictType);
            } else if (fieldValue instanceof List<?> dictValues) {
                List<Map<String, Object>> dictDetails = new ArrayList<>();
                for (Object dictValue : dictValues) {
                    if (dictValue instanceof String) {
                        Map<String, Object> dictDetail = buildDictDetailObject((String) dictValue, dictType);
                        dictDetails.add(dictDetail);
                    }
                }
                return dictDetails.isEmpty() ? null : dictDetails;
            }
        } catch (Exception e) {
            log.warn("[convertDictSelectValue][转换字典值失败] dictType={}, fieldValue={}", dictType, fieldValue, e);
        }

        return buildSimpleDictObject(fieldValue, dictType);
    }

    /**
     * 构建字典详情对象 - 通过查询 system_dict_data 表获取标签
     */
    private Map<String, Object> buildDictDetailObject(String dictValue, String dictType) {
        Map<String, Object> dictDetail = new HashMap<>();
        dictDetail.put("value", dictValue);
        dictDetail.put("dictType", dictType);

        try {
            // 通过 dictType 和 value 查询 system_dict_data 表
            cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO dictData =
                dictDataService.getDictData(dictType, dictValue);

            if (dictData != null) {
                dictDetail.put("label", dictData.getLabel());
                dictDetail.put("sort", dictData.getSort());
                dictDetail.put("status", dictData.getStatus());
                dictDetail.put("colorType", dictData.getColorType());
                dictDetail.put("cssClass", dictData.getCssClass());

            } else {
                // 未找到字典数据，使用原值作为标签
                dictDetail.put("label", dictValue);
            }
        } catch (Exception e) {
            // 查询失败，使用原值作为标签
            dictDetail.put("label", dictValue);
            log.warn("[buildDictDetailObject][查询字典失败] dictType={}, value={}", dictType, dictValue, e);
        }

        return dictDetail;
    }

    /**
     * 构建简单字典对象（当没有字典类型时）
     */
    private Map<String, Object> buildSimpleDictObject(Object fieldValue, String dictType) {
        Map<String, Object> dictDetail = new HashMap<>();
        dictDetail.put("value", fieldValue);
        dictDetail.put("dictType", dictType);
        dictDetail.put("label", fieldValue != null ? fieldValue.toString() : null);

        return dictDetail;
    }

    /**
     * 转换流程选择器值（ProcessSelect/ProcessSelects/ContractSelect）
     * 返回完整的历史变量对象
     */
    private Object convertProcessSelectValue(Object fieldValue) {
        if (fieldValue instanceof String processInstanceId) {
            try {
                // 通过流程实例ID查询历史流程实例
                HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(processInstanceId);
                if (historicProcessInstance != null) {

                    // 获取流程变量
                    Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();
                    if (CollUtil.isNotEmpty(processVariables)) {
                        // 过滤掉系统变量，只返回业务相关的变量
                        Map<String, Object> filteredVariables = new HashMap<>();
                        for (Map.Entry<String, Object> entry : processVariables.entrySet()) {
                            String key = entry.getKey();
                            // 过滤掉系统变量
                            if (!isSystemVariable(key)) {
                                filteredVariables.put(key, entry.getValue());
                            }
                        }

                        // 添加流程实例的基本信息
                        Map<String, Object> result = new HashMap<>();
                        result.put("processInstanceId", processInstanceId);
                        result.put("processInstanceName", historicProcessInstance.getName());
                        result.put("businessKey", historicProcessInstance.getBusinessKey());
                        result.put("startTime", historicProcessInstance.getStartTime());
                        result.put("endTime", historicProcessInstance.getEndTime());
                        result.put("processVariables", filteredVariables);

                        return result;
                    }
                }

                log.warn("[convertProcessSelectValue][未找到流程实例] processInstanceId={}", processInstanceId);
            } catch (Exception e) {
                log.warn("[convertProcessSelectValue][转换流程选择器值失败] processInstanceId={}", processInstanceId, e);
            }
        }
        return fieldValue;
    }

    /**
     * 判断是否为系统变量
     */
    private boolean isSystemVariable(String variableName) {
        // 系统变量列表
        String[] systemVariables = {
            "_FLOWABLE_SKIP_EXPRESSION_ENABLED",
            "PROCESS_START_USER_ID",
            "processInstanceStatus",
            "processInstanceReason"
        };

        for (String systemVar : systemVariables) {
            if (systemVar.equals(variableName)) {
                return true;
            }
        }

        // 以下划线开头的通常是系统变量
        return variableName.startsWith("_") && variableName.length() > 1;
    }

    /**
     * 转换组嵌套值 - 递归处理组内的字段，支持复杂嵌套结构
     * 使用从表单字段中解析出的类型信息进行转换
     */
    @SuppressWarnings("unchecked")
    private Object convertGroupValue(Object fieldValue, Map<String, Map<String, Object>> fieldOptionsMap) {
        // 获取当前处理上下文中的字段类型映射
        Map<String, String> fieldTypeMap = getCurrentFieldTypeMap();

        if (fieldValue instanceof Map) {
            // 处理 Map 类型的嵌套对象
            Map<String, Object> groupData = (Map<String, Object>) fieldValue;
            Map<String, Object> result = new HashMap<>();

            for (Map.Entry<String, Object> entry : groupData.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 递归处理各种类型的值
                Object processedValue = processNestedValue(key, value, fieldTypeMap);
                result.put(key, processedValue);
            }

            return result;
        } else if (fieldValue instanceof List) {
            // 处理 List 类型的数组数据
            List<?> listData = (List<?>) fieldValue;
            List<Object> result = new ArrayList<>();

            for (Object item : listData) {
                Object processedItem = convertGroupValue(item, fieldOptionsMap);
                result.add(processedItem);
            }

            return result;
        } else {
            // 基本类型，直接返回
            return fieldValue;
        }
    }

    /**
     * 处理嵌套值，支持各种数据结构
     * 使用从表单字段中解析出的类型信息进行转换
     */
    @SuppressWarnings("unchecked")
    private Object processNestedValue(String key, Object value, Map<String, String> fieldTypeMap) {
        if (value instanceof List) {
            // 处理数组类型（如 tableForm 的数据）
            List<?> listValue = (List<?>) value;
            List<Object> processedList = new ArrayList<>();

            for (Object item : listValue) {
                if (item instanceof Map) {
                    // 处理数组中的对象
                    Map<String, Object> itemMap = (Map<String, Object>) item;
                    Map<String, Object> processedItem = new HashMap<>();

                    for (Map.Entry<String, Object> itemEntry : itemMap.entrySet()) {
                        String itemKey = itemEntry.getKey();
                        Object itemValue = itemEntry.getValue();

                        // 从字段类型映射中获取字段类型
                        String fieldType = fieldTypeMap.get(itemKey);
                        if (StrUtil.isNotBlank(fieldType) && itemValue != null) {
                            Object convertedValue = convertFieldByType(fieldType, itemValue, fieldTypeMap);
                            if (convertedValue != null && !convertedValue.equals(itemValue)) {
                                processedItem.put(itemKey, itemValue); // 原值
                                processedItem.put(itemKey + "_detail", convertedValue); // 详情
                            } else {
                                processedItem.put(itemKey, itemValue);
                            }
                        } else {
                            processedItem.put(itemKey, itemValue);
                        }
                    }

                    processedList.add(processedItem);
                } else {
                    processedList.add(item);
                }
            }

            return processedList;
        } else if (value instanceof Map) {
            // 递归处理嵌套的 Map
            return convertGroupValue(value, null); // 传递 null，因为我们现在使用 fieldTypeMap
        } else {
            // 检查是否为支持的选择器类型
            String fieldType = fieldTypeMap.get(key);
            if (StrUtil.isNotBlank(fieldType) && value != null) {
                Object convertedValue = convertFieldByType(fieldType, value, fieldTypeMap);
                if (convertedValue != null && !convertedValue.equals(value)) {
                    // 对于基本字段，我们需要在上层处理 _detail 字段
                    return value; // 这里只返回原值，_detail 在上层添加
                }
            }
            return value;
        }
    }

    // 线程本地变量，用于在递归过程中传递字段类型映射
    private static final ThreadLocal<Map<String, String>> FIELD_TYPE_MAP_CONTEXT = new ThreadLocal<>();

    /**
     * 获取当前线程的字段类型映射
     */
    private Map<String, String> getCurrentFieldTypeMap() {
        return FIELD_TYPE_MAP_CONTEXT.get();
    }

    /**
     * 设置当前线程的字段类型映射
     */
    private void setCurrentFieldTypeMap(Map<String, String> fieldTypeMap) {
        FIELD_TYPE_MAP_CONTEXT.set(fieldTypeMap);
    }

    /**
     * 清理当前线程的字段类型映射
     */
    private void clearCurrentFieldTypeMap() {
        FIELD_TYPE_MAP_CONTEXT.remove();
    }

    /**
     * 根据字段类型转换值（支持字典类型）
     */
    private Object convertFieldByType(String fieldType, Object fieldValue, Map<String, String> fieldTypeMap) {
        switch (fieldType) {
            case "UserSelect":
            case "CurrentUserSelect":
                return convertUserSelectValue(fieldValue);
            case "DeptSelect":
            case "CurrentUserDeptSelect":
                return convertDeptSelectValue(fieldValue);
            case "ProcessSelect":
            case "ProcessSelects":
            case "ContractSelect":
                return convertProcessSelectValue(fieldValue);
            case "DictSelect":
                String dictType = fieldTypeMap.get(fieldValue + "_dictType");
                return convertDictSelectValue(fieldValue, dictType);
            default:
                return null;
        }
    }

}
