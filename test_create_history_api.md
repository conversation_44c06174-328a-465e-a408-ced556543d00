# 测试同步历史流程实例接口优化

## 问题描述
原始的 `admin-api/bpm/process-instance/create-history` 接口在处理只有开始事件和结束事件（没有审批节点）的流程时会报错：

```
java.lang.RuntimeException: org.flowable.common.engine.api.FlowableObjectNotFoundException: execution 8ff72bbd-6d35-11f0-9f22-d6aaec4244b3 doesn't exist
```

## 根本原因
错误发生在 `BpmTaskServiceImpl.moveTaskToEnd()` 方法中：
1. 当流程只有开始事件和结束事件时，`getRunningTaskListByProcessInstanceId()` 返回空列表
2. 后续代码尝试访问 `taskList.get(0).getProcessDefinitionId()` 导致 IndexOutOfBoundsException
3. 这个异常被 FlowableUtils.executeAuthenticatedUserId 包装成 RuntimeException

## 解决方案
在 `BpmProcessInstanceServiceImpl.createHistoryProcessInstance()` 方法中：

1. **新增专用方法**: 创建了 `moveHistoryProcessInstanceToEnd()` 方法专门处理历史流程实例的结束逻辑
2. **智能判断**: 先检查是否有运行中的任务
   - 有任务：使用原有的 `taskService.moveTaskToEnd()` 逻辑
   - 无任务：使用新的处理逻辑
3. **兜底机制**: 多层异常处理确保流程能够正常结束

## 优化后的处理流程

### 情况一：有审批节点的流程
```
创建流程实例 → 检测到有运行任务 → 使用原有 moveTaskToEnd 逻辑 → 完成
```

### 情况二：只有开始/结束事件的流程
```
创建流程实例 → 检测到无运行任务 → 获取BPMN模型 → 找到结束事件 → 移动执行实例到结束事件 → 完成
```

### 情况三：异常兜底
```
任何步骤失败 → 强制删除流程实例 → 记录日志 → 继续执行后续逻辑
```

## 测试建议

### 测试用例1：正常审批流程
```json
{
  "processDefinitionId": "有审批节点的流程定义ID",
  "startUserId": 1,
  "variables": {},
  "startTime": "2025-01-01 10:00:00",
  "endTime": "2025-01-01 18:00:00",
  "businessKey": "TEST_001",
  "processInstanceName": "测试审批流程"
}
```

### 测试用例2：简单流程（只有开始/结束事件）
```json
{
  "processDefinitionId": "只有开始结束事件的流程定义ID",
  "startUserId": 1,
  "variables": {},
  "startTime": "2025-01-01 10:00:00",
  "endTime": "2025-01-01 18:00:00",
  "businessKey": "TEST_002",
  "processInstanceName": "测试简单流程"
}
```

## 验证要点

1. **接口调用成功**: 不再抛出 FlowableObjectNotFoundException
2. **流程实例状态**: 历史表中流程实例状态为已完成
3. **时间设置正确**: 开始时间和结束时间按请求参数设置
4. **日志记录**: 查看日志确认走了正确的处理分支

## 相关文件修改

- `BpmProcessInstanceServiceImpl.java`: 主要修改文件
  - 修改 `createHistoryProcessInstance()` 方法
  - 新增 `moveHistoryProcessInstanceToEnd()` 方法
  - 添加 Execution 类导入

## 注意事项

1. 这个优化是向后兼容的，不会影响现有的正常审批流程
2. 增加了详细的日志记录，便于问题排查
3. 采用了多层异常处理，确保系统稳定性
4. 保持了原有的事务处理机制
