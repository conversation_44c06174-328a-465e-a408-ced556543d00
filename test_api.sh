#!/bin/bash

# 测试同步历史流程实例接口
# 使用方法: ./test_api.sh

# 配置
BASE_URL="http://localhost:48080"
API_PATH="/admin-api/bpm/process-instance/create-history"
CONTENT_TYPE="application/json"

# 获取访问令牌（需要根据实际情况修改）
# TOKEN="your_access_token_here"

echo "=== 测试同步历史流程实例接口 ==="
echo "URL: ${BASE_URL}${API_PATH}"
echo ""

# 测试数据 - 请根据实际的流程定义ID修改
TEST_DATA='{
  "processDefinitionId": "_ItemList:4:12bff701-6d35-11f0-bb97-badec6124442",
  "startUserId": 1,
  "variables": {
    "processInstanceName": "测试3"
  },
  "startTime": "2025-01-01 10:00:00",
  "endTime": "2025-01-01 18:00:00",
  "businessKey": "TEST_HISTORY_001",
  "processInstanceName": "历史数据同步测试"
}'

echo "测试数据:"
echo "$TEST_DATA" | jq '.' 2>/dev/null || echo "$TEST_DATA"
echo ""

# 发送请求
echo "发送请求..."
if [ -n "$TOKEN" ]; then
    RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X POST \
        -H "Content-Type: $CONTENT_TYPE" \
        -H "Authorization: Bearer $TOKEN" \
        -d "$TEST_DATA" \
        "${BASE_URL}${API_PATH}")
else
    RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X POST \
        -H "Content-Type: $CONTENT_TYPE" \
        -d "$TEST_DATA" \
        "${BASE_URL}${API_PATH}")
fi

# 解析响应
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容:"
echo "$BODY" | jq '.' 2>/dev/null || echo "$BODY"

# 判断结果
if [ "$HTTP_CODE" = "200" ]; then
    echo ""
    echo "✅ 测试成功！接口调用正常"
    
    # 尝试解析流程实例ID
    PROCESS_INSTANCE_ID=$(echo "$BODY" | jq -r '.data' 2>/dev/null)
    if [ "$PROCESS_INSTANCE_ID" != "null" ] && [ -n "$PROCESS_INSTANCE_ID" ]; then
        echo "📋 创建的流程实例ID: $PROCESS_INSTANCE_ID"
    fi
else
    echo ""
    echo "❌ 测试失败！HTTP状态码: $HTTP_CODE"
    
    # 检查是否是之前的错误
    if echo "$BODY" | grep -q "FlowableObjectNotFoundException\|execution.*doesn't exist"; then
        echo "🔍 检测到原始错误，说明修复未生效"
    fi
fi

echo ""
echo "=== 测试完成 ==="

# 提示
echo ""
echo "💡 使用提示:"
echo "1. 请确保服务已启动在 $BASE_URL"
echo "2. 请根据实际情况修改 processDefinitionId"
echo "3. 如需认证，请设置 TOKEN 变量"
echo "4. 可以查看应用日志确认处理分支"
